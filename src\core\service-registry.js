/**
 * Permisos Digitales Service Registry
 * 
 * Central registration of all application services
 * Defines service dependencies and initialization order
 * 
 * Updated by Agent 3: Container Integrator
 * Integrates with Agent 1's config system and Agent 2's fixed services
 */

const { logger } = require('../utils/logger');
const unifiedConfig = require('../config/unified-config');

/**
 * Register all application services with the service container
 * @param {PermisosServiceContainer} serviceContainer - The service container instance
 * @param {Object} config - Initialized configuration from unified config system
 */
function registerPermisosServices(serviceContainer, config) {
  logger.info('[ServiceRegistry] Registering application services...', {
    configProvided: !!config,
    environment: config?.env || 'unknown'
  });

  // Database Service - Foundation service, no dependencies
  serviceContainer.registerService('database', async (deps, config) => {
    // Use the existing database connection system
    const db = require('../db');
    
    // Pass the unified config to the database module
    db.setConfig(config);
    
    const connectionMonitor = require('../services/connection-monitor.service');
    
    // Register database for monitoring
    connectionMonitor.registerConnection('postgresql', {
      type: 'database',
      checkHealth: async () => {
        try {
          await db.query('SELECT 1 as health_check');
          return true;
        } catch (error) {
          return false;
        }
      },
      reconnect: async () => {
        // Use the new recreatePool function for proper reconnection
        await db.recreatePool();
      },
      onDisconnect: async () => {
        logger.warn('[Database] Connection lost, preparing for reconnection');
      },
      onReconnect: async () => {
        logger.info('[Database] Connection restored');
      }
    });
    
    logger.info('[ServiceRegistry] Database service registered with connection monitoring');
    return {
      query: (text, params) => db.query(text, params),
      getPool: () => db.getPool(),
      getHealthStatus: async () => {
        try {
          const result = await db.query('SELECT 1 as health_check');
          return { healthy: true, connectionPool: db.getPool() };
        } catch (error) {
          return { healthy: false, error: error.message };
        }
      },
      shutdown: async () => {
        connectionMonitor.stopMonitoring('postgresql');
        if (db.getPool()) {
          await db.getPool().end();
        }
      }
    };
  }, {
    dependencies: [],
    priority: 1,
    optional: false,
    timeout: 30000,
    healthCheck: (service) => service.getHealthStatus(),
    shutdownHandler: (service) => service.shutdown()
  });

  // Redis Service - Independent cache service  
  serviceContainer.registerService('redis', async (deps, config) => {
    const redisClient = require('../utils/redis-client');
    const connectionMonitor = require('../services/connection-monitor.service');
    
    // Register Redis for monitoring
    connectionMonitor.registerConnection('redis', {
      type: 'redis',
      checkHealth: async () => {
        try {
          if (redisClient.ping) {
            await redisClient.ping();
            return true;
          }
          return redisClient.getStatus ? redisClient.getStatus().healthy : true;
        } catch (error) {
          return false;
        }
      },
      reconnect: async () => {
        if (redisClient.connect) {
          await redisClient.connect();
        }
      },
      onDisconnect: async () => {
        logger.warn('[Redis] Connection lost, preparing for reconnection');
      },
      onReconnect: async () => {
        logger.info('[Redis] Connection restored');
      }
    });
    
    logger.info('[ServiceRegistry] Redis service registered with connection monitoring');
    return {
      client: redisClient,
      getHealthStatus: () => redisClient.getStatus ? redisClient.getStatus() : { healthy: true },
      shutdown: () => {
        connectionMonitor.stopMonitoring('redis');
        if (redisClient.disconnect) {
          return redisClient.disconnect();
        }
      }
    };
  }, {
    dependencies: [],
    priority: 2,
    optional: false, // Redis is REQUIRED in production
    timeout: 15000,
    healthCheck: (service) => service.getHealthStatus(),
    shutdownHandler: (service) => service.shutdown()
  });

  // Stripe Payment Service - Payment processing (Agent 2 fixed)
  serviceContainer.registerService('stripePayment', async (deps, config) => {
    const stripeService = require('../services/stripe-payment.service'); // This is already an instance
    
    // Initialize with config if not already initialized
    if (!stripeService.initialized) {
      try {
        // The service has an initializeStripe method that needs to be called
        if (typeof stripeService.initializeStripe === 'function') {
          await stripeService.initializeStripe();
        } else {
          // If no initialize method, it may be already ready
          logger.warn('[ServiceRegistry] Stripe service has no initializeStripe method, assuming ready');
        }
      } catch (error) {
        // In test environment, stripe might not be configured
        if (config.env === 'test' || process.env.NODE_ENV === 'test') {
          logger.warn('[ServiceRegistry] Stripe service initialization failed in test environment, continuing...', {
            error: error.message
          });
          // Create a mock service for tests
          Object.assign(stripeService, {
            initialized: true,
            stripe: {
              createCustomer: async () => ({ id: 'test_customer' }),
              createPaymentIntent: async () => ({ id: 'test_payment_intent' })
            }
          });
        } else {
          throw error; // Re-throw in production
        }
      }
    }
    logger.info('[ServiceRegistry] Stripe payment service registered and initialized');
    
    return stripeService;
  }, {
    dependencies: ['database'], // Stripe needs database for payment tracking
    priority: 10,
    optional: false,
    timeout: 20000,
    healthCheck: (service) => {
      return {
        healthy: service.initialized || true, // Assume healthy if no initialized flag
        initialized: service.initialized || true,
        metrics: service.getMetrics ? service.getMetrics() : {}
      };
    },
    shutdownHandler: (service) => {
      if (service.shutdown) {
        return service.shutdown();
      }
    }
  });

  // Audit Service - Logging and tracking admin actions
  serviceContainer.registerService('auditService', async (deps, config) => {
    const AuditService = require('../services/audit.service');
    const auditService = new AuditService({
      database: deps.database,
      redis: deps.redis,
      logger: logger
    });
    logger.info('[ServiceRegistry] Audit service registered');
    return auditService;
  }, {
    dependencies: ['database', 'redis'],
    priority: 14,
    optional: false,
    timeout: 10000,
    healthCheck: (service) => {
      return { healthy: true, serviceName: 'auditService' };
    }
  });

  // Authentication Service - Depends on database
  serviceContainer.registerService('authService', async (deps, config) => {
    const authSecurityService = require('../services/auth-security.service');
    logger.info('[ServiceRegistry] Auth security service registered');
    return authSecurityService;
  }, {
    dependencies: ['database'],
    priority: 15,
    optional: false,
    timeout: 10000,
    healthCheck: (service) => ({ healthy: true, available: !!service })
  });

  // Email Service - Agent 2 fixed (config race condition resolved)
  serviceContainer.registerService('emailService', async (deps, config) => {
    const emailServiceExports = require('../services/email.service');
    
    // Email service exports an object with service instance
    const emailService = emailServiceExports.service || emailServiceExports;
    
    // Initialize only if we have the initialize method and not yet initialized
    if (emailService.initialize && typeof emailService.initialize === 'function' && !emailService.initialized) {
      await emailService.initialize();
    }
    logger.info('[ServiceRegistry] Email service registered and initialized');
    
    return emailService;
  }, {
    dependencies: ['database'],
    priority: 20,
    optional: true, // Email is optional - can fail without blocking startup
    timeout: 15000,
    healthCheck: (service) => ({ 
      healthy: service.initialized,
      useQueue: service.useQueue,
      transporterReady: !!service.transporter 
    }),
    shutdownHandler: (service) => {
      if (service.shutdown) {
        return service.shutdown();
      }
    }
  });

  // PDF Queue Service - Agent 2 fixed (config race condition resolved)
  serviceContainer.registerService('pdfQueue', async (deps, config) => {
    try {
      const pdfQueueFactory = require('../services/pdf-queue-factory.service');
      if (typeof pdfQueueFactory.createPdfQueueService === 'function') {
        const service = await pdfQueueFactory.createPdfQueueService();
        logger.info('[ServiceRegistry] PDF Queue service registered via factory');
        return service;
      } else {
        // Fallback to development service
        const DevPdfQueueService = require('../services/pdf-queue-dev.service');
        logger.info('[ServiceRegistry] PDF Queue service registered (dev fallback)');
        return DevPdfQueueService;
      }
    } catch (error) {
      // Return stub service for graceful degradation
      logger.warn('[ServiceRegistry] PDF Queue service not available, using stub', { error: error.message });
      return {
        addJob: async () => ({ id: 'stub-job-' + Date.now() }),
        getQueueStats: async () => ({ active: 0, waiting: 0, completed: 0, failed: 0 }),
        getHealthStatus: async () => ({ healthy: false, reason: 'stub_service' }),
        shutdown: async () => {}
      };
    }
  }, {
    dependencies: ['redis'],
    priority: 25,
    optional: false, // PDF generation is REQUIRED in production
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 2000,
    healthCheck: (service) => {
      if (service && typeof service.getQueueStats === 'function') {
        return service.getQueueStats().then(stats => ({
          healthy: true,
          details: stats
        }));
      }
      return { healthy: false, error: 'No health check available' };
    },
    shutdownHandler: (service) => {
      if (service && typeof service.shutdown === 'function') {
        return service.shutdown();
      }
    }
  });

  // Payment Velocity Service - Agent 2 fixed (config race condition resolved)
  serviceContainer.registerService('paymentVelocity', async (deps, config) => {
    const PaymentVelocityService = require('../services/payment-velocity.service');
    const velocityService = new PaymentVelocityService();
    logger.info('[ServiceRegistry] Payment velocity service registered');
    return velocityService;
  }, {
    dependencies: ['redis'],
    priority: 30,
    optional: false, // Payment velocity checks are REQUIRED in production
    timeout: 10000,
    healthCheck: (service) => ({ healthy: true, available: !!service })
  });

  // Alert Service - Agent 2 fixed (config race condition resolved)
  serviceContainer.registerService('alertService', async (deps, config) => {
    const alertService = require('../services/alert.service'); // This is already an instance
    // Alert service initializes itself in constructor, no need to call initialize
    logger.info('[ServiceRegistry] Alert service registered and initialized');
    return alertService;
  }, {
    dependencies: [],
    priority: 35,
    optional: false, // Alert service is REQUIRED in production
    timeout: 10000,
    healthCheck: (service) => ({ 
      healthy: true, 
      channelsInitialized: service.channelsInitialized || false 
    })
  });

  // Queue Monitor - Depends on queue services
  serviceContainer.registerService('queueMonitor', async (deps, config) => {
    const queueMonitorService = require('../services/queue-monitor.service');
    logger.info('[ServiceRegistry] Queue monitor service registered');
    return queueMonitorService;
  }, {
    dependencies: ['pdfQueue'],
    priority: 50,
    optional: true,
    timeout: 10000,
    healthCheck: (service) => ({ healthy: true, available: !!service })
  });

  // Payment Monitoring Service - Depends on database and stripe payment
  serviceContainer.registerService('paymentMonitoring', async (deps, config) => {
    const paymentMonitoringService = require('../services/payment-monitoring.service');
    logger.info('[ServiceRegistry] Payment monitoring service registered');
    return paymentMonitoringService;
  }, {
    dependencies: ['database', 'stripePayment'],
    priority: 40,
    optional: true,
    timeout: 10000,
    healthCheck: (service) => ({ healthy: true, available: !!service })
  });

  logger.info('[ServiceRegistry] All services registered successfully', {
    totalServices: serviceContainer.factories.size
  });
}

module.exports = {
  registerPermisosServices
};