/* Enhanced Footer Component Styles */
.appFooter {
  background-color: var(--bs-gray-50, #f8f9fa);
  color: var(--bs-gray-700);
  border-top: 1px solid var(--bs-gray-200);
  margin-top: auto; /* Push footer to bottom */
}

.footerContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem 2rem;
}

/* Main footer content */
.footerMain {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

/* Footer sections */
.footerSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footerSectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-gray-900);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.footerNav {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footerLink {
  font-size: 0.95rem;
  color: var(--bs-gray-600);
  text-decoration: none;
  transition: color 0.2s ease;
  padding: 0.25rem 0;
}

.footerLink:hover,
.footerLink:focus {
  color: var(--rojo);
  text-decoration: none;
}

/* External link indicator */
.footerLink[target="_blank"]::after {
  content: " ↗";
  font-size: 0.85em;
  opacity: 0.7;
}

/* Footer bottom */
.footerBottom {
  border-top: 1px solid var(--bs-gray-200);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyrightText {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
  margin: 0;
}

.supportText {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .footerContainer {
    padding: 2rem 1rem 1.5rem;
  }

  .footerMain {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footerSection {
    align-items: center;
  }

  .footerBottom {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .footerContainer {
    padding: 1.5rem 1rem;
  }

  .footerSectionTitle {
    font-size: 0.95rem;
  }

  .footerLink {
    font-size: 0.9rem;
  }
}