import React, { useEffect, Suspense } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';

import ProtectedRoute from './components/auth/ProtectedRoute';
import PermitRouteGuard from './components/permit/PermitRouteGuard';
import LoadingSpinner from './components/ui/LoadingSpinner';
import AuthLayout from './layouts/AuthLayout';
import HomeLayout from './layouts/HomeLayout';
import MainLayout from './layouts/MainLayout';
import UserLayout from './layouts/UserLayout';
import VerificationLayout from './layouts/VerificationLayout';
import { cleanupAllTimers, logMemoryUsage } from './utils/memoryCleanup';
import CompletePermitFormPage from './pages/CompletePermitFormPage';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import HelpPage from './pages/HelpPage';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import OxxoConfirmationPage from './pages/OxxoConfirmationPage';
import PaymentErrorPage from './pages/PaymentErrorPage';
import PaymentSuccessPage from './pages/PaymentSuccessPage';
import PermitDetailsPage from './pages/PermitDetailsPage';
import PermitPaymentPage from './pages/PermitPaymentPage';
import PermitsListPage from './pages/PermitsListPage';
import PreVerificationPage from './pages/PreVerificationPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import ProfilePage from './pages/ProfilePage';
import RegisterPage from './pages/RegisterPage';
import ResendVerificationPage from './pages/ResendVerificationPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import TermsAndConditionsPage from './pages/TermsAndConditionsPage';
import UserDashboardPage from './pages/UserDashboardPage';
import UserPermitsPage from './pages/UserPermitsPage';
import VerifyEmailPage from './pages/VerifyEmailPage';
import ResponsiveUserPermitsPage from './pages/ResponsiveUserPermitsPage';
import ResponsivePermitDetailsPage from './pages/ResponsivePermitDetailsPage';
import PermitRenewalPage from './pages/PermitRenewalPage';
import CleanDesignSystem from './pages/CleanDesignSystem';
import PermisosDesignSystem from './pages/PermisosDesignSystem';
import { useUserAuth as useAuth } from './shared/hooks/useAuth';

/**
 * Main application component that defines all routes for the digital permits platform.
 * Handles authentication state and renders appropriate layouts for different user flows.
 */
function App() {
  const { isLoading } = useAuth();
  const location = useLocation();

  // Cleanup timers and log memory on route change
  useEffect(() => {
    // Cleanup any lingering timers when route changes
    cleanupAllTimers();
    
    // Log memory usage in development
    if (process.env.NODE_ENV === 'development') {
      logMemoryUsage();
    }
    
    // Force garbage collection hint (browser may ignore)
    if (window.gc) {
      window.gc();
    }
  }, [location.pathname]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Routes>
      {/* Authentication routes */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />
      </Route>

      {/* Email verification routes */}
      <Route element={<VerificationLayout />}>
        <Route path="/verify-email" element={<VerifyEmailPage />} />
        <Route path="/resend-verification" element={<ResendVerificationPage />} />
        <Route path="/pre-verification" element={<PreVerificationPage />} />
      </Route>

      {/* Protected user routes */}
      <Route element={<ProtectedRoute />}>
        <Route element={<MainLayout />}>
          <Route path="/permits-legacy" element={<PermitsListPage />} />
          <Route path="/payment/success" element={<PaymentSuccessPage />} />
          <Route path="/payment/error" element={<PaymentErrorPage />} />
        </Route>

        <Route element={<UserLayout />}>
          <Route path="/dashboard" element={<UserDashboardPage />} />
          <Route path="/permits" element={<ResponsiveUserPermitsPage />} />
          <Route path="/permits/new" element={<Navigate to="/permits/complete" replace />} />
          <Route path="/permits/complete" element={<CompletePermitFormPage />} />

          <Route path="/permits/:id" element={
            <PermitRouteGuard>
              <ResponsivePermitDetailsPage />
            </PermitRouteGuard>
          } />

          <Route path="/permits/:id/payment" element={
            <PermitRouteGuard>
              <PermitPaymentPage />
            </PermitRouteGuard>
          } />

          <Route path="/permits/:id/renew" element={
            <PermitRouteGuard>
              <PermitRenewalPage />
            </PermitRouteGuard>
          } />

          <Route path="/profile" element={<ProfilePage />} />
        </Route>
      </Route>

      <Route path="/permits/oxxo-confirmation" element={<OxxoConfirmationPage />} />

      {/* Public routes */}
      <Route element={<HomeLayout />}>
        <Route path="/" element={<HomePage />} />
      </Route>

      {/* Public legal and contact pages - accessible without authentication */}
      <Route path="/acerca-de" element={<AboutPage />} />
      <Route path="/ayuda" element={<HelpPage />} />
      <Route path="/terminos-y-condiciones" element={<TermsAndConditionsPage />} />
      <Route path="/politica-de-privacidad" element={<PrivacyPolicyPage />} />
      <Route path="/contacto" element={<ContactPage />} />
      <Route path="/design-system" element={<PermisosDesignSystem />} />
      <Route path="/design-system-generic" element={<CleanDesignSystem />} />

    </Routes>
  );
}

export default App;
