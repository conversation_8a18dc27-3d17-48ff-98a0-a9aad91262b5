/* Mobile Permit Card - Following established design system patterns */

.card {
  background-color: var(--color-white);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  margin-bottom: var(--space-3);
  min-height: 140px; /* Ensure adequate minimum height */
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

/* Card Header */
.cardHeader {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3); /* Increased gap for better spacing */
  padding: var(--space-4) var(--space-3); /* Increased vertical padding */
  background-color: var(--color-neutral-50);
  border-bottom: 1px solid var(--color-neutral-100);
  flex-wrap: wrap;
  min-height: 80px; /* Ensure adequate height for content */
}

.vehicleIcon {
  width: 40px;
  height: 40px;
  min-width: 40px;
  background-color: var(--color-primary-lightest);
  color: var(--color-primary);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.headerContent {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-1); /* Use consistent spacing variable */
  min-height: 60px; /* Ensure enough height for 3-line title + 2-line name */
}

.vehicleTitle {
  font-size: 1rem; /* 16px - Increased for better readability */
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
  margin: 0;
  line-height: 1.4; /* Increased line height for better spacing */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Allow 3 lines instead of 2 */
  -webkit-box-orient: vertical;
  white-space: normal;
  max-height: 4.2rem; /* 3 lines * 1.4 line-height * 1rem */
}

.ownerName {
  font-size: 0.875rem; /* 14px - Increased from 13px */
  color: var(--color-neutral-600);
  margin: 0;
  line-height: 1.3; /* Better line height */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Allow 2 lines for longer names */
  -webkit-box-orient: vertical;
  white-space: normal;
  max-height: 2.6rem; /* 2 lines * 1.3 line-height * 0.875rem */
}

.statusBadge {
  flex-shrink: 0;
  margin-left: auto;
  font-size: 0.75rem !important; /* 12px - Increased for better readability */
  padding: 4px 10px !important;
  max-width: 140px; /* Increased to accommodate longer text */
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Hide status icon on very small screens to save space */
@media (max-width: 360px) {
  .statusBadge svg {
    display: none;
  }
}

/* Card Body */
.cardBody {
  padding: var(--space-4) var(--space-3); /* Increased vertical padding */
}

.dataRows {
  display: flex;
  flex-direction: column;
  gap: var(--space-2); /* Increased gap for better spacing */
}

.dataRow {
  display: flex;
  align-items: flex-start; /* Align to top for multi-line values */
  gap: 8px; /* Increased gap */
  font-size: 0.875rem; /* 14px - Increased from 13px */
  line-height: 1.4;
  min-height: 20px; /* Ensure consistent row height */
}

.dataIcon {
  color: var(--color-neutral-500);
  width: 16px; /* Increased for better visibility */
  min-width: 16px;
  font-size: 16px;
  margin-top: 1px; /* Fine-tune alignment */
}

.dataLabel {
  color: var(--color-neutral-600);
  min-width: 50px; /* Increased for better alignment */
  flex-shrink: 0;
  font-weight: var(--font-weight-medium);
}

.dataValue {
  color: var(--color-neutral-900);
  font-weight: var(--font-weight-medium);
  word-break: break-word; /* Allow breaking of long values */
  overflow-wrap: break-word;
  flex: 1;
  line-height: 1.3;
}

/* Expiration Warning */
.expirationWarning {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: var(--space-2);
  padding: 6px 10px;
  background-color: var(--color-info-lightest);
  color: var(--color-info-dark);
  border-radius: var(--border-radius);
  font-size: 0.75rem; /* 12px */
  font-weight: var(--font-weight-medium);
  line-height: 1.3;
}

.expirationWarning.expiringSoon {
  background-color: var(--color-warning-lightest);
  color: var(--color-warning-dark);
}

.expirationWarning svg {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

/* Card Footer */
.cardFooter {
  padding: var(--space-3);
  border-top: 1px solid var(--color-neutral-100);
  background-color: var(--color-neutral-50);
}

.paymentButton {
  width: 100%;
  justify-content: center;
  font-size: 0.9375rem !important; /* 15px - Increased for better readability */
  padding: 12px 16px !important; /* Increased padding */
  min-height: 44px; /* Meeting touch target requirements */
  font-weight: var(--font-weight-semibold);
}

/* Extra optimizations for very small screens */
@media (max-width: 375px) {
  .card {
    margin-bottom: var(--space-2);
  }

  .cardHeader {
    padding: var(--space-3);
    gap: var(--space-2);
    min-height: 76px; /* Slightly reduced but still adequate */
  }
  
  .vehicleIcon {
    width: 36px;
    height: 36px;
    min-width: 36px;
    font-size: 1.125rem;
  }
  
  .vehicleTitle {
    font-size: 0.9375rem; /* 15px - Keep reasonably large */
  }

  .ownerName {
    font-size: 0.8125rem; /* 13px - Keep readable */
  }

  .statusBadge {
    font-size: 0.6875rem !important; /* 11px */
    padding: 3px 7px !important;
  }
  
  .cardBody {
    padding: var(--space-3);
  }
  
  .dataRow {
    font-size: 0.8125rem; /* 13px - Keep readable */
  }

  .dataLabel {
    min-width: 42px;
  }

  .expirationWarning {
    font-size: 0.75rem; /* 12px - Keep readable */
    padding: 6px 10px;
  }

  .cardFooter {
    padding: var(--space-3);
  }

  .paymentButton {
    font-size: 0.875rem !important; /* 14px - Keep readable */
    padding: 10px 14px !important;
    min-height: 44px; /* Maintain touch target */
  }
}

/* Additional optimizations for 360px screens */
@media (max-width: 360px) {
  .card {
    margin-bottom: var(--space-2);
    min-height: 130px; /* Reduced minimum height */
  }

  .cardHeader {
    padding: 12px;
    gap: 10px;
    min-height: 70px;
    flex-wrap: nowrap; /* Prevent wrapping */
  }
  
  .vehicleIcon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    font-size: 1rem;
  }

  .headerContent {
    min-height: 50px; /* Reduced height */
  }
  
  .vehicleTitle {
    font-size: 0.875rem; /* 14px */
    line-height: 1.3;
    -webkit-line-clamp: 2; /* Limit to 2 lines */
    max-height: 2.6rem; /* 2 lines * 1.3 line-height * 0.875rem */
  }

  .ownerName {
    font-size: 0.75rem; /* 12px */
    -webkit-line-clamp: 1; /* Limit to 1 line */
    max-height: 1.3rem;
  }

  .statusBadge {
    font-size: 0.625rem !important; /* 10px */
    padding: 2px 6px !important;
    max-width: 120px;
  }
  
  .cardBody {
    padding: 12px;
  }
  
  .dataRow {
    font-size: 0.75rem; /* 12px */
    gap: 6px;
    min-height: 18px;
  }

  .dataIcon {
    width: 14px;
    min-width: 14px;
    font-size: 14px;
  }

  .dataLabel {
    min-width: 40px;
  }

  .expirationWarning {
    font-size: 0.6875rem; /* 11px */
    padding: 4px 8px;
  }

  .cardFooter {
    padding: 12px;
  }

  .paymentButton {
    font-size: 0.8125rem !important; /* 13px */
    padding: 8px 12px !important;
    min-height: 40px; /* Slightly reduced but still touchable */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .cardHeader {
    border-bottom-width: 2px;
  }
  
  .cardFooter {
    border-top-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card {
    transition: none;
  }
  
  .card:hover {
    transform: none;
  }
  
  .card:active {
    transform: none;
  }
}