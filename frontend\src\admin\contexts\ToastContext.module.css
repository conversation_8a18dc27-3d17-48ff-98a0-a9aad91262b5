/* Toast Container */
.toastContainer {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  max-width: 350px;
}

/* Toast */
.toast {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-md);
  animation: slideIn var(--animation-duration-slow) ease forwards;
  background-color: var(--color-white);
  border-left: 4px solid var(--color-neutral-300);
}

/* Toast Types */
.toastSuccess {
  border-left-color: var(--color-success);
}

.toastError {
  border-left-color: var(--color-danger);
}

.toastWarning {
  border-left-color: var(--color-warning);
}

.toastInfo {
  border-left-color: var(--color-info);
}

/* Toast Icon */
.toastIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: var(--space-3);
  font-weight: var(--font-weight-bold);
}

.toastSuccess .toastIcon {
  color: var(--color-success);
}

.toastError .toastIcon {
  color: var(--color-danger);
}

.toastWarning .toastIcon {
  color: var(--color-warning);
}

.toastInfo .toastIcon {
  color: var(--color-info);
}

/* Toast Message */
.toastMessage {
  flex: 1;
  font-size: var(--font-size-sm);
}

/* Toast Close Button */
.toastClose {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--color-neutral-600);
  margin-left: var(--space-2);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: var(--transition-base);
}

.toastClose:hover {
  color: var(--color-neutral-900);
}

/* Toast Animation */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}
