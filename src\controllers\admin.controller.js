// Admin controller functions will be added here

const ApiResponse = require('../utils/api-response');
const { logger } = require('../utils/logger');
const applicationRepository = require('../repositories/application.repository');

// Will be injected by dependency container
let auditService = null;
const setAuditService = (service) => {
  auditService = service;
};
exports.setAuditService = setAuditService;

/**
 * Get dashboard statistics for admin panel
 */
exports.getDashboardStats = async (req, res) => {
  try {
    logger.info('Admin dashboard stats requested');
    
    const db = require('../db');
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay());
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const thirtyDaysAgo = new Date(now);
    thirtyDaysAgo.setDate(now.getDate() - 30);

    // 1. User Statistics
    const userStatsQuery = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN last_login_at >= $1 THEN 1 END) as active_users,
        COUNT(CASE WHEN created_at >= $2 THEN 1 END) as new_users_today,
        COUNT(CASE WHEN created_at >= $3 THEN 1 END) as new_users_this_week,
        COUNT(CASE WHEN created_at >= $4 THEN 1 END) as new_users_this_month,
        COUNT(CASE WHEN account_type = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN account_type = 'client' THEN 1 END) as client_users,
        COUNT(CASE WHEN is_admin_portal = true THEN 1 END) as admin_portal_users
      FROM users
    `;
    const userStatsResult = await db.query(userStatsQuery, [
      thirtyDaysAgo,
      today,
      thisWeekStart,
      thisMonthStart
    ]);
    const userStats = userStatsResult.rows[0];

    // 2. Application Statistics
    const applicationStatsQuery = `
      SELECT 
        COUNT(*) as total_applications,
        COUNT(CASE WHEN status = 'PENDING_PAYMENT' THEN 1 END) as pending_payment,
        COUNT(CASE WHEN status = 'PAYMENT_RECEIVED' THEN 1 END) as payment_received,
        COUNT(CASE WHEN status = 'GENERATING_PERMIT' THEN 1 END) as generating_permit,
        COUNT(CASE WHEN status = 'PERMIT_READY' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'ERROR_GENERATING_PERMIT' THEN 1 END) as failed,
        COUNT(CASE WHEN status = 'PAYMENT_EXPIRED' THEN 1 END) as expired,
        COUNT(CASE WHEN created_at >= $1 THEN 1 END) as applications_today,
        COUNT(CASE WHEN created_at >= $2 THEN 1 END) as applications_this_week,
        COUNT(CASE WHEN created_at >= $3 THEN 1 END) as applications_this_month
      FROM permit_applications
    `;
    const applicationStatsResult = await db.query(applicationStatsQuery, [
      today,
      thisWeekStart,
      thisMonthStart
    ]);
    const applicationStats = applicationStatsResult.rows[0];

    // 3. Financial Statistics
    const financialStatsQuery = `
      SELECT 
        COALESCE(SUM(CASE WHEN pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') THEN pa.total_amount END), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') AND pa.created_at >= $1 THEN pa.total_amount END), 0) as revenue_today,
        COALESCE(SUM(CASE WHEN pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') AND pa.created_at >= $2 THEN pa.total_amount END), 0) as revenue_this_week,
        COALESCE(SUM(CASE WHEN pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') AND pa.created_at >= $3 THEN pa.total_amount END), 0) as revenue_this_month,
        COALESCE(AVG(CASE WHEN pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') THEN pa.total_amount END), 0) as average_application_value,
        COUNT(DISTINCT CASE WHEN pa.payment_method = 'credit_card' AND pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') THEN pa.id END) as credit_card_payments,
        COUNT(DISTINCT CASE WHEN pa.payment_method = 'oxxo_cash' AND pa.status IN ('PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY') THEN pa.id END) as oxxo_payments
      FROM permit_applications pa
    `;
    const financialStatsResult = await db.query(financialStatsQuery, [
      today,
      thisWeekStart,
      thisMonthStart
    ]);
    const financialStats = financialStatsResult.rows[0];

    // 4. System Health Statistics
    // Failed permits from failed_permits table
    const failedPermitsQuery = `
      SELECT 
        COUNT(*) as total_failed_permits,
        COUNT(CASE WHEN resolution_status = 'pending' THEN 1 END) as pending_resolution,
        COUNT(CASE WHEN resolution_status = 'resolved' THEN 1 END) as resolved_failures
      FROM failed_permits
    `;
    const failedPermitsResult = await db.query(failedPermitsQuery);
    const failedPermitsStats = failedPermitsResult.rows[0];

    // Email delivery statistics
    const emailStatsQuery = `
      SELECT 
        COUNT(*) as total_emails,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_emails,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_emails,
        COUNT(CASE WHEN status = 'bounce' THEN 1 END) as bounced_emails
      FROM email_notifications
      WHERE created_at >= $1
    `;
    const emailStatsResult = await db.query(emailStatsQuery, [thirtyDaysAgo]);
    const emailStats = emailStatsResult.rows[0];

    // PDF generation statistics
    const pdfStatsQuery = `
      SELECT 
        COUNT(*) as total_pdf_jobs,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_pdfs,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_pdfs,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_pdfs,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_pdfs
      FROM pdf_generation_queue
      WHERE created_at >= $1
    `;
    const pdfStatsResult = await db.query(pdfStatsQuery, [thirtyDaysAgo]);
    const pdfStats = pdfStatsResult.rows[0];

    // Active sessions count
    const activeSessionsQuery = `
      SELECT COUNT(*) as active_sessions
      FROM sessions
      WHERE expire > $1
    `;
    const activeSessionsResult = await db.query(activeSessionsQuery, [now]);
    const activeSessions = activeSessionsResult.rows[0].active_sessions;

    // Recent activity (last 10 events)
    const recentActivityQuery = `
      SELECT 
        'application_created' as event_type,
        pa.id as entity_id,
        pa.created_at,
        u.email as user_email,
        u.first_name,
        u.last_name,
        pa.status
      FROM permit_applications pa
      JOIN users u ON pa.user_id = u.id
      ORDER BY pa.created_at DESC
      LIMIT 10
    `;
    const recentActivityResult = await db.query(recentActivityQuery);
    const recentActivity = recentActivityResult.rows;

    // Calculate percentages and rates
    const totalApplications = parseInt(applicationStats.total_applications) || 1; // Avoid division by zero
    const failureRate = totalApplications > 0 
      ? ((parseInt(failedPermitsStats.total_failed_permits) / totalApplications) * 100).toFixed(2)
      : 0;

    const totalEmails = parseInt(emailStats.total_emails) || 1;
    const emailDeliveryRate = totalEmails > 0
      ? ((parseInt(emailStats.sent_emails) / totalEmails) * 100).toFixed(2)
      : 0;

    const totalPdfJobs = parseInt(pdfStats.total_pdf_jobs) || 1;
    const pdfSuccessRate = totalPdfJobs > 0
      ? ((parseInt(pdfStats.completed_pdfs) / totalPdfJobs) * 100).toFixed(2)
      : 0;

    // Compile all statistics
    const stats = {
      users: {
        total: parseInt(userStats.total_users),
        active: parseInt(userStats.active_users),
        newToday: parseInt(userStats.new_users_today),
        newThisWeek: parseInt(userStats.new_users_this_week),
        newThisMonth: parseInt(userStats.new_users_this_month),
        byType: {
          admin: parseInt(userStats.admin_users),
          client: parseInt(userStats.client_users),
          adminPortal: parseInt(userStats.admin_portal_users)
        }
      },
      applications: {
        total: parseInt(applicationStats.total_applications),
        byStatus: {
          pendingPayment: parseInt(applicationStats.pending_payment),
          paymentReceived: parseInt(applicationStats.payment_received),
          generatingPermit: parseInt(applicationStats.generating_permit),
          completed: parseInt(applicationStats.completed),
          failed: parseInt(applicationStats.failed),
          expired: parseInt(applicationStats.expired)
        },
        newToday: parseInt(applicationStats.applications_today),
        newThisWeek: parseInt(applicationStats.applications_this_week),
        newThisMonth: parseInt(applicationStats.applications_this_month)
      },
      financial: {
        totalRevenue: parseFloat(financialStats.total_revenue),
        revenueToday: parseFloat(financialStats.revenue_today),
        revenueThisWeek: parseFloat(financialStats.revenue_this_week),
        revenueThisMonth: parseFloat(financialStats.revenue_this_month),
        averageApplicationValue: parseFloat(financialStats.average_application_value),
        paymentMethods: {
          creditCard: parseInt(financialStats.credit_card_payments),
          oxxo: parseInt(financialStats.oxxo_payments)
        }
      },
      systemHealth: {
        failedPermits: {
          total: parseInt(failedPermitsStats.total_failed_permits),
          pending: parseInt(failedPermitsStats.pending_resolution),
          resolved: parseInt(failedPermitsStats.resolved_failures),
          failureRate: parseFloat(failureRate)
        },
        emailDelivery: {
          total: parseInt(emailStats.total_emails),
          sent: parseInt(emailStats.sent_emails),
          failed: parseInt(emailStats.failed_emails),
          bounced: parseInt(emailStats.bounced_emails),
          deliveryRate: parseFloat(emailDeliveryRate)
        },
        pdfGeneration: {
          total: parseInt(pdfStats.total_pdf_jobs),
          completed: parseInt(pdfStats.completed_pdfs),
          failed: parseInt(pdfStats.failed_pdfs),
          processing: parseInt(pdfStats.processing_pdfs),
          pending: parseInt(pdfStats.pending_pdfs),
          successRate: parseFloat(pdfSuccessRate)
        },
        activeSessions: parseInt(activeSessions)
      },
      recentActivity: recentActivity.map(activity => ({
        type: activity.event_type,
        entityId: activity.entity_id,
        timestamp: activity.created_at,
        user: {
          email: activity.user_email,
          name: `${activity.first_name} ${activity.last_name}`
        },
        status: activity.status
      })),
      generatedAt: now.toISOString()
    };
    
    logger.info('Dashboard stats retrieved successfully', {
      totalUsers: stats.users.total,
      totalApplications: stats.applications.total,
      totalRevenue: stats.financial.totalRevenue
    });
    
    // Log audit action
    if (auditService) {
      await auditService.logAdminAction(
        req.session.userId,
        'view',
        'dashboard_stats',
        null,
        null,
        req
      );
    }
    
    return ApiResponse.success(res, stats);
  } catch (error) {
    logger.error('Error getting dashboard stats:', error);
    return ApiResponse.error(res, 'Error al obtener estadísticas del dashboard', 500);
  }
};

/**
 * Get all applications with filtering and pagination
 */
exports.getAllApplications = async (req, res) => {
  try {
    const adminId = req.session.userId;
    logger.info(`Admin ${adminId} requesting all applications with filters:`, req.query);
    
    // Extract query parameters
    const {
      page = 1,
      limit = 20,
      status,
      paymentStatus,
      startDate,
      endDate,
      search,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;
    
    // Validate pagination parameters
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = Math.min(parseInt(limit, 10) || 20, 100); // Max 100 records per page
    const offset = (pageNum - 1) * limitNum;
    
    // Build query
    const params = [];
    let countQuery = `
      SELECT COUNT(DISTINCT pa.id)
      FROM permit_applications pa
      JOIN users u ON pa.user_id = u.id
      LEFT JOIN payment_events pe ON pa.id = pe.application_id
      WHERE 1=1
    `;
    
    let query = `
      SELECT DISTINCT
        pa.id,
        pa.user_id,
        pa.status,
        pa.created_at,
        pa.updated_at,
        pa.payment_reference,
        pa.payment_processor_order_id,
        pa.nombre_completo,
        pa.curp_rfc,
        pa.marca,
        pa.linea,
        pa.ano_modelo,
        pa.color,
        pa.numero_serie,
        pa.numero_motor,
        pa.placas,
        pa.domicilio,
        pa.importe,
        pa.folio,
        pa.fecha_expedicion,
        pa.fecha_vencimiento,
        pa.permit_file_path,
        pa.certificado_file_path,
        pa.placas_file_path,
        pa.queue_status,
        pa.queue_error,
        pa.puppeteer_error_at,
        pa.puppeteer_error_message,
        u.email as user_email,
        u.phone as user_phone,
        CONCAT(u.first_name, ' ', u.last_name) as user_full_name,
        u.account_type as user_account_type,
        u.created_at as user_created_at,
        (
          SELECT COUNT(*) 
          FROM permit_applications 
          WHERE user_id = pa.user_id
        ) as user_total_applications,
        (
          SELECT pe2.event_type 
          FROM payment_events pe2 
          WHERE pe2.application_id = pa.id 
          ORDER BY pe2.created_at DESC 
          LIMIT 1
        ) as last_payment_event,
        (
          SELECT pe3.created_at 
          FROM payment_events pe3 
          WHERE pe3.application_id = pa.id 
          ORDER BY pe3.created_at DESC 
          LIMIT 1
        ) as last_payment_event_date
      FROM permit_applications pa
      JOIN users u ON pa.user_id = u.id
      LEFT JOIN payment_events pe ON pa.id = pe.application_id
      WHERE 1=1
    `;
    
    // Apply filters
    if (status) {
      params.push(status);
      query += ` AND pa.status = $${params.length}`;
      countQuery += ` AND pa.status = $${params.length}`;
    }
    
    // Payment status filter based on application status
    if (paymentStatus) {
      const paymentStatuses = {
        'pending': ['AWAITING_PAYMENT', 'AWAITING_OXXO_PAYMENT', 'PAYMENT_PROCESSING'],
        'completed': ['PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY', 'COMPLETED'],
        'failed': ['PAYMENT_FAILED']
      };
      
      if (paymentStatuses[paymentStatus]) {
        params.push(paymentStatuses[paymentStatus]);
        query += ` AND pa.status = ANY($${params.length}::text[])`;
        countQuery += ` AND pa.status = ANY($${params.length}::text[])`;
      }
    }
    
    if (startDate) {
      params.push(startDate);
      query += ` AND pa.created_at >= $${params.length}::date`;
      countQuery += ` AND pa.created_at >= $${params.length}::date`;
    }
    
    if (endDate) {
      params.push(endDate);
      query += ` AND pa.created_at <= ($${params.length}::date + interval '1 day')`;
      countQuery += ` AND pa.created_at <= ($${params.length}::date + interval '1 day')`;
    }
    
    if (search) {
      const searchParam = `%${search}%`;
      params.push(searchParam);
      params.push(search); // For exact ID match
      const searchCondition = ` AND (
        pa.nombre_completo ILIKE $${params.length - 1} OR
        pa.curp_rfc ILIKE $${params.length - 1} OR
        pa.placas ILIKE $${params.length - 1} OR
        u.email ILIKE $${params.length - 1} OR
        u.phone ILIKE $${params.length - 1} OR
        CONCAT(u.first_name, ' ', u.last_name) ILIKE $${params.length - 1} OR
        CAST(pa.id AS TEXT) = $${params.length}
      )`;
      query += searchCondition;
      countQuery += searchCondition;
    }
    
    // Validate and apply sorting
    const allowedSortFields = ['created_at', 'updated_at', 'status', 'id', 'importe'];
    const sortField = allowedSortFields.includes(sortBy) ? `pa.${sortBy}` : 'pa.created_at';
    const sortDirection = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    query += ` ORDER BY ${sortField} ${sortDirection}`;
    
    // Add pagination
    const queryParams = [...params];
    queryParams.push(limitNum);
    queryParams.push(offset);
    query += ` LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length}`;
    
    // Execute queries
    const db = require('../db');
    const countResult = await db.query(countQuery, params);
    const total = parseInt(countResult.rows[0].count, 10);
    const { rows } = await db.query(query, queryParams);
    
    // Transform data for frontend
    const applications = rows.map(row => ({
      id: row.id,
      userId: row.user_id,
      status: row.status,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      paymentReference: row.payment_reference,
      paymentProcessorOrderId: row.payment_processor_order_id,
      
      // Application details
      personalInfo: {
        nombreCompleto: row.nombre_completo,
        curpRfc: row.curp_rfc,
        domicilio: row.domicilio
      },
      
      vehicleInfo: {
        marca: row.marca,
        linea: row.linea,
        anoModelo: row.ano_modelo,
        color: row.color,
        numeroSerie: row.numero_serie,
        numeroMotor: row.numero_motor,
        placas: row.placas
      },
      
      permitInfo: {
        importe: row.importe,
        folio: row.folio,
        fechaExpedicion: row.fecha_expedicion,
        fechaVencimiento: row.fecha_vencimiento,
        permitFilePath: row.permit_file_path,
        certificadoFilePath: row.certificado_file_path,
        placasFilePath: row.placas_file_path
      },
      
      // User information
      user: {
        email: row.user_email,
        phone: row.user_phone,
        fullName: row.user_full_name,
        accountType: row.user_account_type,
        createdAt: row.user_created_at,
        totalApplications: row.user_total_applications
      },
      
      // Processing status
      processing: {
        queueStatus: row.queue_status,
        queueError: row.queue_error,
        puppeteerErrorAt: row.puppeteer_error_at,
        puppeteerErrorMessage: row.puppeteer_error_message
      },
      
      // Payment information
      payment: {
        lastEvent: row.last_payment_event,
        lastEventDate: row.last_payment_event_date
      }
    }));
    
    const response = {
      data: applications,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum),
        hasNextPage: pageNum * limitNum < total,
        hasPreviousPage: pageNum > 1
      },
      filters: {
        status,
        paymentStatus,
        startDate,
        endDate,
        search
      },
      sort: {
        field: sortBy,
        order: sortOrder
      }
    };
    
    // Log audit action
    if (auditService) {
      await auditService.logAdminAction(
        req.session.userId,
        'view',
        'application_list',
        null,
        { filters: req.query, resultCount: applications.length },
        req
      );
    }
    
    return ApiResponse.success(res, response);
  } catch (error) {
    logger.error('Error getting all applications:', error);
    return ApiResponse.error(res, 'Error al obtener aplicaciones', 500);
  }
};

/**
 * Get failed applications
 * TODO: Implement logic to retrieve failed applications
 */
exports.getFailedApplications = async (req, res) => {
  try {
    logger.info('Admin requesting failed applications');
    
    // Stub implementation - return empty array
    const failedApplications = {
      data: [],
      total: 0
    };
    
    return ApiResponse.success(res, failedApplications);
  } catch (error) {
    logger.error('Error getting failed applications:', error);
    return ApiResponse.error(res, 'Error al obtener aplicaciones fallidas', 500);
  }
};

/**
 * Get detailed information about a specific application
 */
exports.getApplicationDetails = async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id, 10);
    const adminId = req.session.userId;
    
    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }
    
    logger.info(`Admin ${adminId} requesting details for application ${applicationId}`);
    
    const db = require('../db');
    
    // Get comprehensive application details
    const applicationQuery = `
      SELECT 
        pa.*,
        u.email as user_email,
        u.phone as user_phone,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        u.account_type as user_account_type,
        u.is_email_verified as user_email_verified,
        u.account_status as user_account_status,
        u.created_at as user_created_at,
        u.updated_at as user_updated_at,
        u.last_login_at as user_last_login_at,
        u.login_count as user_login_count,
        (
          SELECT COUNT(*) 
          FROM permit_applications 
          WHERE user_id = pa.user_id
        ) as user_total_applications,
        (
          SELECT COUNT(*) 
          FROM permit_applications 
          WHERE user_id = pa.user_id 
          AND status = 'PERMIT_READY'
        ) as user_completed_applications,
        (
          SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', pa2.id,
              'status', pa2.status,
              'created_at', pa2.created_at,
              'folio', pa2.folio,
              'fecha_vencimiento', pa2.fecha_vencimiento
            ) ORDER BY pa2.created_at DESC
          )
          FROM permit_applications pa2
          WHERE pa2.user_id = pa.user_id
          AND pa2.id != pa.id
          LIMIT 5
        ) as user_recent_applications
      FROM permit_applications pa
      JOIN users u ON pa.user_id = u.id
      WHERE pa.id = $1
    `;
    
    const appResult = await db.query(applicationQuery, [applicationId]);
    
    if (appResult.rows.length === 0) {
      return ApiResponse.notFound(res, 'Aplicación no encontrada');
    }
    
    const application = appResult.rows[0];
    
    // Get payment history
    const paymentQuery = `
      SELECT 
        pe.id,
        pe.event_type,
        pe.event_data,
        pe.created_at,
        pe.order_id,
        pe.webhook_id,
        pe.webhook_status,
        pe.webhook_attempts,
        pe.webhook_last_attempt_at,
        pe.webhook_next_retry_at,
        pe.webhook_error
      FROM payment_events pe
      WHERE pe.application_id = $1
      ORDER BY pe.created_at DESC
    `;
    
    const paymentResult = await db.query(paymentQuery, [applicationId]);
    
    // Get PDF generation history
    const pdfHistoryQuery = `
      SELECT 
        pq.id,
        pq.status,
        pq.created_at,
        pq.started_at,
        pq.completed_at,
        pq.failed_at,
        pq.error_message,
        pq.attempts,
        pq.pdf_type,
        pq.file_path,
        pq.file_size,
        pq.processing_time_ms,
        pq.admin_id,
        pq.manual_upload,
        u.email as admin_email
      FROM pdf_queue pq
      LEFT JOIN users u ON pq.admin_id = u.id
      WHERE pq.application_id = $1
      ORDER BY pq.created_at DESC
    `;
    
    const pdfResult = await db.query(pdfHistoryQuery, [applicationId]);
    
    // Get related permits (renewals)
    const relatedPermitsQuery = `
      SELECT 
        pa.id,
        pa.status,
        pa.folio,
        pa.created_at,
        pa.fecha_expedicion,
        pa.fecha_vencimiento,
        pa.renewal_count,
        'renewal' as relation_type
      FROM permit_applications pa
      WHERE pa.renewed_from_id = $1
      
      UNION ALL
      
      SELECT 
        pa.id,
        pa.status,
        pa.folio,
        pa.created_at,
        pa.fecha_expedicion,
        pa.fecha_vencimiento,
        pa.renewal_count,
        'original' as relation_type
      FROM permit_applications pa
      WHERE pa.id = (
        SELECT renewed_from_id 
        FROM permit_applications 
        WHERE id = $1
      )
      
      ORDER BY created_at DESC
    `;
    
    const relatedResult = await db.query(relatedPermitsQuery, [applicationId]);
    
    // Get payment recovery attempts if any
    const recoveryQuery = `
      SELECT 
        pra.id,
        pra.attempt_number,
        pra.status as recovery_status,
        pra.initiated_at,
        pra.completed_at,
        pra.error_message,
        pra.recovery_method,
        pra.admin_user_id,
        u.email as admin_email
      FROM payment_recovery_attempts pra
      LEFT JOIN users u ON pra.admin_user_id = u.id
      WHERE pra.application_id = $1
      ORDER BY pra.initiated_at DESC
    `;
    
    const recoveryResult = await db.query(recoveryQuery, [applicationId]);
    
    // Get email reminders sent
    const remindersQuery = `
      SELECT 
        er.id,
        er.reminder_type,
        er.sent_at,
        er.email_sent_to,
        er.status as email_status,
        er.error_message as email_error
      FROM email_reminders er
      WHERE er.application_id = $1
      ORDER BY er.sent_at DESC
    `;
    
    const remindersResult = await db.query(remindersQuery, [applicationId]);
    
    // Transform the data
    const response = {
      // Basic application info
      id: application.id,
      status: application.status,
      createdAt: application.created_at,
      updatedAt: application.updated_at,
      expiresAt: application.expires_at,
      
      // Personal information
      personalInfo: {
        nombreCompleto: application.nombre_completo,
        curpRfc: application.curp_rfc,
        domicilio: application.domicilio
      },
      
      // Vehicle information
      vehicleInfo: {
        marca: application.marca,
        linea: application.linea,
        anoModelo: application.ano_modelo,
        color: application.color,
        numeroSerie: application.numero_serie,
        numeroMotor: application.numero_motor,
        placas: application.placas,
        tipoVehiculo: application.tipo_vehiculo
      },
      
      // Permit information
      permitInfo: {
        importe: application.importe,
        folio: application.folio,
        fechaExpedicion: application.fecha_expedicion,
        fechaVencimiento: application.fecha_vencimiento,
        permitFilePath: application.permit_file_path,
        certificadoFilePath: application.certificado_file_path,
        placasFilePath: application.placas_file_path,
        recomendacionesFilePath: application.recomendaciones_file_path
      },
      
      // Payment information
      paymentInfo: {
        paymentReference: application.payment_reference,
        paymentProcessorOrderId: application.payment_processor_order_id,
        paymentInitiatedAt: application.payment_initiated_at,
        paymentCompletedAt: application.payment_completed_at
      },
      
      // User information
      user: {
        id: application.user_id,
        email: application.user_email,
        phone: application.user_phone,
        firstName: application.user_first_name,
        lastName: application.user_last_name,
        fullName: `${application.user_first_name} ${application.user_last_name}`,
        accountType: application.user_account_type,
        emailVerified: application.user_email_verified,
        accountStatus: application.user_account_status,
        createdAt: application.user_created_at,
        updatedAt: application.user_updated_at,
        lastLoginAt: application.user_last_login_at,
        loginCount: application.user_login_count,
        totalApplications: application.user_total_applications,
        completedApplications: application.user_completed_applications,
        recentApplications: application.user_recent_applications || []
      },
      
      // Processing information
      processing: {
        queueStatus: application.queue_status,
        queuePosition: application.queue_position,
        queueError: application.queue_error,
        queueEnteredAt: application.queue_entered_at,
        queueStartedAt: application.queue_started_at,
        queueCompletedAt: application.queue_completed_at,
        queueDurationMs: application.queue_duration_ms,
        queueJobId: application.queue_job_id
      },
      
      // Error information
      errors: {
        puppeteerErrorAt: application.puppeteer_error_at,
        puppeteerErrorMessage: application.puppeteer_error_message,
        puppeteerScreenshotPath: application.puppeteer_screenshot_path
      },
      
      // Admin actions
      adminActions: {
        resolvedAt: application.resolved_at,
        resolvedByAdmin: application.resolved_by_admin,
        adminResolutionNotes: application.admin_resolution_notes
      },
      
      // Renewal information
      renewalInfo: {
        renewedFromId: application.renewed_from_id,
        renewalCount: application.renewal_count,
        isRenewal: !!application.renewed_from_id
      },
      
      // History and related data
      paymentHistory: paymentResult.rows.map(row => ({
        id: row.id,
        eventType: row.event_type,
        eventData: row.event_data,
        createdAt: row.created_at,
        orderId: row.order_id,
        webhook: {
          id: row.webhook_id,
          status: row.webhook_status,
          attempts: row.webhook_attempts,
          lastAttemptAt: row.webhook_last_attempt_at,
          nextRetryAt: row.webhook_next_retry_at,
          error: row.webhook_error
        }
      })),
      
      pdfGenerationHistory: pdfResult.rows.map(row => ({
        id: row.id,
        status: row.status,
        createdAt: row.created_at,
        startedAt: row.started_at,
        completedAt: row.completed_at,
        failedAt: row.failed_at,
        errorMessage: row.error_message,
        attempts: row.attempts,
        pdfType: row.pdf_type,
        filePath: row.file_path,
        fileSize: row.file_size,
        processingTimeMs: row.processing_time_ms,
        adminId: row.admin_id,
        adminEmail: row.admin_email,
        manualUpload: row.manual_upload
      })),
      
      relatedPermits: relatedResult.rows.map(row => ({
        id: row.id,
        status: row.status,
        folio: row.folio,
        createdAt: row.created_at,
        fechaExpedicion: row.fecha_expedicion,
        fechaVencimiento: row.fecha_vencimiento,
        renewalCount: row.renewal_count,
        relationType: row.relation_type
      })),
      
      paymentRecoveryAttempts: recoveryResult.rows.map(row => ({
        id: row.id,
        attemptNumber: row.attempt_number,
        status: row.recovery_status,
        initiatedAt: row.initiated_at,
        completedAt: row.completed_at,
        errorMessage: row.error_message,
        recoveryMethod: row.recovery_method,
        adminUserId: row.admin_user_id,
        adminEmail: row.admin_email
      })),
      
      emailReminders: remindersResult.rows.map(row => ({
        id: row.id,
        reminderType: row.reminder_type,
        sentAt: row.sent_at,
        emailSentTo: row.email_sent_to,
        status: row.email_status,
        errorMessage: row.email_error
      }))
    };
    
    // Log audit action
    if (auditService) {
      await auditService.logAdminAction(
        req.session.userId,
        'view',
        'application',
        applicationId,
        null,
        req
      );
    }
    
    return ApiResponse.success(res, response);
  } catch (error) {
    logger.error('Error getting application details:', error);
    return ApiResponse.error(res, 'Error al obtener detalles de la aplicación', 500);
  }
};

/**
 * Retry PDF generation using Puppeteer
 * TODO: Implement retry logic
 */
exports.retryPuppeteer = async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id, 10);
    
    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }
    
    logger.info(`Admin retrying Puppeteer for application ${applicationId}`);
    
    // Stub implementation
    return ApiResponse.success(res, {
      message: 'Retry iniciado',
      applicationId,
      status: 'pending'
    });
  } catch (error) {
    logger.error('Error retrying Puppeteer:', error);
    return ApiResponse.error(res, 'Error al reintentar generación de PDF', 500);
  }
};

/**
 * Mark an application as resolved
 * TODO: Implement resolution logic
 */
exports.markApplicationResolved = async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id, 10);
    
    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }
    
    logger.info(`Admin marking application ${applicationId} as resolved`);
    
    // Stub implementation
    return ApiResponse.success(res, {
      message: 'Aplicación marcada como resuelta',
      applicationId
    });
  } catch (error) {
    logger.error('Error marking application as resolved:', error);
    return ApiResponse.error(res, 'Error al marcar aplicación como resuelta', 500);
  }
};

/**
 * Upload manual PDFs for an application
 * TODO: Implement file upload and storage logic
 */
exports.uploadManualPDFs = async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id, 10);
    
    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }
    
    logger.info(`Admin uploading PDFs for application ${applicationId}`);
    
    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return ApiResponse.badRequest(res, 'No se proporcionaron archivos');
    }
    
    // Stub implementation
    return ApiResponse.success(res, {
      message: 'PDFs cargados exitosamente',
      applicationId,
      filesUploaded: req.files.length
    });
  } catch (error) {
    logger.error('Error uploading manual PDFs:', error);
    return ApiResponse.error(res, 'Error al cargar PDFs', 500);
  }
};

/**
 * Manually trigger PDF generation for an application
 */
exports.triggerPdfGeneration = async (req, res) => {
  try {
    const adminId = req.session.userId;
    const applicationId = parseInt(req.params.id, 10);

    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }

    logger.info(`Admin ${adminId} manually triggering PDF generation for application ${applicationId}`);

    // Check if application exists
    const application = await applicationRepository.findById(applicationId);
    if (!application) {
      return ApiResponse.notFound(res, 'Aplicación no encontrada');
    }

    // Validate application status
    const validStatuses = ['PAYMENT_RECEIVED', 'ERROR_GENERATING_PERMIT', 'GENERATING_PERMIT'];
    if (!validStatuses.includes(application.status)) {
      return ApiResponse.badRequest(res, 
        `Estado inválido para generar PDF. Estado actual: ${application.status}. ` +
        `Estados válidos: ${validStatuses.join(', ')}`
      );
    }

    // Check if PDF already exists
    if (application.permit_file_path) {
      return ApiResponse.badRequest(res, 'El permiso ya fue generado');
    }

    // Update status to GENERATING_PERMIT
    await applicationRepository.updateApplicationStatus(applicationId, 'GENERATING_PERMIT');

    // Queue the permit generation job using the service
    const { getInstance } = require('../services/pdf-queue-factory.service');
    const pdfQueueService = getInstance();
    
    // Add to queue with metadata
    const jobData = {
      applicationId,
      userId: application.user_id,
      triggeredBy: 'admin',
      adminId: adminId,
      originalStatus: application.status
    };
    
    await pdfQueueService.addJob(jobData);

    logger.info(`PDF generation queued for application ${applicationId} by admin ${adminId}`);

    // Log audit action
    if (auditService) {
      await auditService.logAdminAction(
        adminId,
        'update',
        'application',
        applicationId,
        {
          action: 'trigger_pdf_generation',
          previousStatus: application.status,
          newStatus: 'GENERATING_PERMIT'
        },
        req
      );
    }

    return ApiResponse.success(res, {
      message: 'Generación de PDF agregada a la cola',
      applicationId,
      status: 'queued',
      triggeredBy: 'admin'
    });

  } catch (error) {
    logger.error('Error triggering PDF generation:', error);
    return ApiResponse.error(res, 'Error al iniciar generación de PDF', 500);
  }
};

/**
 * Update application status with validation and notifications
 */
exports.updateApplicationStatus = async (req, res) => {
  try {
    const applicationId = parseInt(req.params.id, 10);
    const adminId = req.session.userId;
    const { status, reason, notify = true } = req.body;
    
    if (isNaN(applicationId)) {
      return ApiResponse.badRequest(res, 'ID de aplicación inválido');
    }
    
    if (!status) {
      return ApiResponse.badRequest(res, 'Estado es requerido');
    }
    
    logger.info(`Admin ${adminId} updating status for application ${applicationId} to ${status}`);
    
    // Validate the new status
    const { ApplicationStatus, ApplicationHelpers } = require('../constants/application.constants');
    const validStatuses = Object.values(ApplicationStatus);
    
    if (!validStatuses.includes(status)) {
      return ApiResponse.badRequest(res, `Estado inválido: ${status}`);
    }
    
    const db = require('../db');
    const client = await db.dbPool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Get current application state with lock
      const currentAppQuery = `
        SELECT 
          pa.*,
          u.email as user_email,
          u.first_name as user_first_name,
          u.last_name as user_last_name,
          u.phone as user_phone
        FROM permit_applications pa
        JOIN users u ON pa.user_id = u.id
        WHERE pa.id = $1
        FOR UPDATE
      `;
      
      const currentAppResult = await client.query(currentAppQuery, [applicationId]);
      
      if (currentAppResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return ApiResponse.notFound(res, 'Aplicación no encontrada');
      }
      
      const currentApp = currentAppResult.rows[0];
      const previousStatus = currentApp.status;
      
      // Check if status change is allowed
      if (previousStatus === status) {
        await client.query('ROLLBACK');
        return ApiResponse.badRequest(res, 'La aplicación ya tiene este estado');
      }
      
      // Some status transitions may not be allowed
      const restrictedTransitions = {
        'COMPLETED': ['CANCELLED', 'EXPIRED'], // Can't cancel or expire completed permits
        'CANCELLED': [], // Can't change cancelled applications
        'EXPIRED': ['CANCELLED'], // Can't cancel expired permits
        'VENCIDO': ['CANCELLED'] // Can't cancel vencido permits
      };
      
      if (restrictedTransitions[previousStatus] && restrictedTransitions[previousStatus].includes(status)) {
        await client.query('ROLLBACK');
        return ApiResponse.badRequest(res, `No se puede cambiar de ${previousStatus} a ${status}`);
      }
      
      // Update the status
      const updateQuery = `
        UPDATE permit_applications
        SET 
          status = $1,
          updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;
      
      const updateResult = await client.query(updateQuery, [
        status,
        applicationId
      ]);
      
      // Create a system payment event for tracking
      const eventQuery = `
        INSERT INTO payment_events (
          application_id,
          order_id,
          event_type,
          event_data,
          created_at
        ) VALUES ($1, $2, $3, $4, NOW())
      `;
      
      await client.query(eventQuery, [
        applicationId,
        currentApp.payment_processor_order_id || 'ADMIN_ACTION',
        'admin.status.updated',
        JSON.stringify({
          previousStatus,
          newStatus: status,
          adminId,
          reason: reason || 'Manual status update',
          timestamp: new Date().toISOString()
        })
      ]);
      
      await client.query('COMMIT');
      
      // Send notifications if requested
      if (notify && currentApp.user_email) {
        try {
          const notificationService = require('../services/notification.service');
          
          // Determine notification type based on status change
          let notificationType = 'status_update';
          let emailTemplate = 'status-update';
          
          if (status === 'PERMIT_READY') {
            notificationType = 'permit_ready';
            emailTemplate = 'permit-ready';
          } else if (status === 'PAYMENT_FAILED') {
            notificationType = 'payment_failed';
            emailTemplate = 'payment-failed';
          } else if (status === 'CANCELLED') {
            notificationType = 'application_cancelled';
            emailTemplate = 'application-cancelled';
          }
          
          await notificationService.sendNotification({
            type: notificationType,
            recipientEmail: currentApp.user_email,
            recipientPhone: currentApp.user_phone,
            data: {
              applicationId,
              previousStatus,
              newStatus: status,
              userName: `${currentApp.user_first_name} ${currentApp.user_last_name}`,
              vehicleInfo: `${currentApp.marca} ${currentApp.linea} ${currentApp.ano_modelo}`,
              reason: reason || 'Actualización administrativa',
              adminAction: true
            },
            emailTemplate
          });
          
          logger.info(`Notification sent for status update on application ${applicationId}`);
        } catch (notificationError) {
          // Don't fail the request if notification fails
          logger.error('Error sending notification:', notificationError);
        }
      }
      
      return ApiResponse.success(res, {
        message: 'Estado actualizado exitosamente',
        applicationId,
        previousStatus,
        newStatus: status,
        notificationSent: notify
      });
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
    
  } catch (error) {
    logger.error('Error updating application status:', error);
    return ApiResponse.error(res, 'Error al actualizar estado de la aplicación', 500);
  }
};

/**
 * Export applications to CSV with filters
 */
exports.exportApplications = async (req, res) => {
  try {
    const adminId = req.session.userId;
    logger.info(`Admin ${adminId} exporting applications with filters:`, req.query);
    
    // Extract same filters as getAllApplications
    const {
      status,
      paymentStatus,
      startDate,
      endDate,
      search,
      format = 'csv'
    } = req.query;
    
    // Build query (similar to getAllApplications but without pagination)
    const params = [];
    let query = `
      SELECT 
        pa.id,
        pa.status,
        pa.created_at,
        pa.updated_at,
        pa.payment_reference,
        pa.payment_processor_order_id,
        pa.nombre_completo,
        pa.curp_rfc,
        pa.domicilio,
        pa.marca,
        pa.linea,
        pa.ano_modelo,
        pa.color,
        pa.numero_serie,
        pa.numero_motor,
        pa.placas,
        pa.tipo_vehiculo,
        pa.importe,
        pa.folio,
        pa.fecha_expedicion,
        pa.fecha_vencimiento,
        pa.queue_status,
        pa.puppeteer_error_at,
        pa.puppeteer_error_message,
        pa.renewed_from_id,
        pa.renewal_count,
        u.email as user_email,
        u.phone as user_phone,
        CONCAT(u.first_name, ' ', u.last_name) as user_full_name,
        u.account_type as user_account_type,
        u.created_at as user_created_at,
        (
          SELECT pe.event_type 
          FROM payment_events pe 
          WHERE pe.application_id = pa.id 
          ORDER BY pe.created_at DESC 
          LIMIT 1
        ) as last_payment_event,
        (
          SELECT pe.created_at 
          FROM payment_events pe 
          WHERE pe.application_id = pa.id 
          ORDER BY pe.created_at DESC 
          LIMIT 1
        ) as last_payment_event_date
      FROM permit_applications pa
      JOIN users u ON pa.user_id = u.id
      WHERE 1=1
    `;
    
    // Apply same filters as getAllApplications
    if (status) {
      params.push(status);
      query += ` AND pa.status = $${params.length}`;
    }
    
    if (paymentStatus) {
      const paymentStatuses = {
        'pending': ['AWAITING_PAYMENT', 'AWAITING_OXXO_PAYMENT', 'PAYMENT_PROCESSING'],
        'completed': ['PAYMENT_RECEIVED', 'GENERATING_PERMIT', 'PERMIT_READY', 'COMPLETED'],
        'failed': ['PAYMENT_FAILED']
      };
      
      if (paymentStatuses[paymentStatus]) {
        params.push(paymentStatuses[paymentStatus]);
        query += ` AND pa.status = ANY($${params.length}::text[])`;
      }
    }
    
    if (startDate) {
      params.push(startDate);
      query += ` AND pa.created_at >= $${params.length}::date`;
    }
    
    if (endDate) {
      params.push(endDate);
      query += ` AND pa.created_at <= ($${params.length}::date + interval '1 day')`;
    }
    
    if (search) {
      const searchParam = `%${search}%`;
      params.push(searchParam);
      params.push(search);
      query += ` AND (
        pa.nombre_completo ILIKE $${params.length - 1} OR
        pa.curp_rfc ILIKE $${params.length - 1} OR
        pa.placas ILIKE $${params.length - 1} OR
        u.email ILIKE $${params.length - 1} OR
        u.phone ILIKE $${params.length - 1} OR
        CONCAT(u.first_name, ' ', u.last_name) ILIKE $${params.length - 1} OR
        CAST(pa.id AS TEXT) = $${params.length}
      )`;
    }
    
    query += ' ORDER BY pa.created_at DESC';
    
    // Execute query
    const db = require('../db');
    const { rows } = await db.query(query, params);
    
    if (format === 'csv') {
      // Create CSV content
      const csvHeaders = [
        'ID',
        'Estado',
        'Fecha Creación',
        'Fecha Actualización',
        'Referencia Pago',
        'ID Orden Pago',
        'Nombre Completo',
        'CURP/RFC',
        'Domicilio',
        'Marca',
        'Línea',
        'Año Modelo',
        'Color',
        'Número Serie',
        'Número Motor',
        'Placas',
        'Tipo Vehículo',
        'Importe',
        'Folio',
        'Fecha Expedición',
        'Fecha Vencimiento',
        'Estado Cola',
        'Error Puppeteer',
        'Fecha Error',
        'Es Renovación',
        'Número Renovación',
        'Email Usuario',
        'Teléfono Usuario',
        'Nombre Usuario',
        'Tipo Cuenta',
        'Fecha Registro Usuario',
        'Último Evento Pago',
        'Fecha Último Evento'
      ];
      
      // Helper function to escape CSV values
      const escapeCSV = (value) => {
        if (value === null || value === undefined) return '';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };
      
      // Build CSV rows
      const csvRows = rows.map(row => [
        row.id,
        row.status,
        row.created_at ? new Date(row.created_at).toLocaleString('es-MX') : '',
        row.updated_at ? new Date(row.updated_at).toLocaleString('es-MX') : '',
        row.payment_reference,
        row.payment_processor_order_id,
        row.nombre_completo,
        row.curp_rfc,
        row.domicilio,
        row.marca,
        row.linea,
        row.ano_modelo,
        row.color,
        row.numero_serie,
        row.numero_motor,
        row.placas,
        row.tipo_vehiculo,
        row.importe,
        row.folio,
        row.fecha_expedicion ? new Date(row.fecha_expedicion).toLocaleDateString('es-MX') : '',
        row.fecha_vencimiento ? new Date(row.fecha_vencimiento).toLocaleDateString('es-MX') : '',
        row.queue_status,
        row.puppeteer_error_message,
        row.puppeteer_error_at ? new Date(row.puppeteer_error_at).toLocaleString('es-MX') : '',
        row.renewed_from_id ? 'Sí' : 'No',
        row.renewal_count || 0,
        row.user_email,
        row.user_phone,
        row.user_full_name,
        row.user_account_type,
        row.user_created_at ? new Date(row.user_created_at).toLocaleString('es-MX') : '',
        row.last_payment_event,
        row.last_payment_event_date ? new Date(row.last_payment_event_date).toLocaleString('es-MX') : ''
      ].map(escapeCSV).join(','));
      
      // Combine headers and rows
      const csvContent = [
        csvHeaders.join(','),
        ...csvRows
      ].join('\n');
      
      // Add UTF-8 BOM for proper Excel compatibility
      const bom = '\ufeff';
      const csvWithBom = bom + csvContent;
      
      // Set response headers
      const filename = `aplicaciones_${new Date().toISOString().split('T')[0]}.csv`;
      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      return res.send(csvWithBom);
    } else {
      // Return JSON format
      return ApiResponse.success(res, {
        count: rows.length,
        filters: {
          status,
          paymentStatus,
          startDate,
          endDate,
          search
        },
        data: rows
      });
    }
    
  } catch (error) {
    logger.error('Error exporting applications:', error);
    return ApiResponse.error(res, 'Error al exportar aplicaciones', 500);
  }
};