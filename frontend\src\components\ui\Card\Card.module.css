/* Card.module.css - "Soft & Trustworthy" / "Calm Waters" Aligned */

/* --- Base Card Styles --- */
.card {
  background-color: var(--calm-waters-card-bg, var(--color-white));
  border: 1px solid var(--calm-waters-card-border, var(--color-neutral-200));
  border-radius: var(--border-radius-lg); /* Standard large radius */
  margin-bottom: var(--space-4); /* Default bottom margin */
  box-shadow: var(--box-shadow-sm); /* Subtle default shadow */
  transition:
    box-shadow 0.25s ease-out,
    transform 0.25s ease-out;
  overflow: hidden; /* Important if children have sharp corners or might overflow */
  width: 100%; /* Default to full width of its container */
  box-sizing: border-box;
  max-width: 100%;
}

.cardHover:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
  border-color: var(--color-neutral-300);
}

/* --- Card Sections --- */
.cardHeader {
  padding: var(--space-3) var(--space-4); /* Consistent padding: 12px 16px */
  border-bottom: 1px solid var(--calm-waters-card-border, var(--color-neutral-200));
  background-color: var(
    --calm-waters-accent,
    var(--color-neutral-50)
  ); /* Subtle accent or light gray */
}

.cardTitle {
  /* Default styling for title if rendered by Card.tsx */
  font-family: var(--font-family-headings);
  font-size: var(--font-size-lg); /* e.g., 1.25rem */
  font-weight: var(--font-weight-semibold);
  color: var(--calm-waters-text-primary, var(--color-neutral-800));
  margin: 0; /* Remove default heading margin */
  line-height: 1.3;
}

.cardBody {
  padding: var(--space-4); /* Default body padding: 16px */
}

.cardFooter {
  padding: var(--space-3) var(--space-4);
  border-top: 1px solid var(--calm-waters-card-border, var(--color-neutral-200));
  background-color: var(--calm-waters-accent, var(--color-neutral-50));
}

/* --- Auth Variant --- */
.cardAuth {
  /* Auth variant might be narrower on desktop and centered */
  max-width: 480px; /* Example max-width for auth forms on desktop */
  margin: var(--space-5) auto var(--space-5) auto;
  box-shadow: var(--box-shadow); /* More pronounced shadow for auth */
  position: relative; /* For the ::before pseudo-element */
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* Top accent border for Auth Card */
.cardAuth::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px; /* Thicker accent */
  background-color: var(--color-primary);

  /* border-top-left-radius: var(--border-radius-lg); /* Match card's radius */

  /* border-top-right-radius: var(--border-radius-lg); /* Match card's radius */

  /* No need for radius on the ::before if the card has overflow:hidden */
}

.cardHeaderAuth {
  /* Specific styling for auth card header */
  background-color: transparent;
  border-bottom: none;
  padding-top: var(--space-5); /* More top padding */
  padding-bottom: var(--space-3);
  text-align: center;
}

.cardHeaderAuth .cardTitle {
  /* Title within auth header */
  font-size: var(--font-size-xl); /* Larger title for auth forms */
}

.cardBodyAuth {
  /* Specific padding for auth card body */
  padding: var(--space-3) var(--space-5) var(--space-5);
}

/* .cardFooterAuth can be added if auth cards need a distinct footer style */

/* --- Responsive Styles for Default Card --- */
@media (max-width: var(--breakpoint-sm)) {
  /* Mobile: up to 480px */
  .card {
    margin-bottom: var(--space-3);
    border-radius: var(--border-radius); /* Slightly smaller radius on mobile */
  }

  .cardHeader {
    padding: var(--space-2) var(--space-3);
  }

  .cardTitle {
    font-size: var(--font-size-base); /* Adjust title size for mobile */
  }

  .cardBody {
    padding: var(--space-3);
  }

  .cardFooter {
    padding: var(--space-2) var(--space-3);
  }

  /* Auth card specific mobile adjustments */
  .cardAuth {
    width: 100%; /* Full width on small mobile */
    margin: var(--space-3) 0 var(--space-3) 0;
    border-radius: var(--border-radius); /* Consistent mobile radius */
    max-width: 100%;
    overflow-x: hidden; /* Prevent horizontal overflow */
  }

  .cardHeaderAuth {
    padding-top: var(--space-4);
    padding-bottom: var(--space-2);
  }

  .cardHeaderAuth .cardTitle {
    font-size: var(--font-size-lg); /* Adjust for mobile */
  }

  .cardBodyAuth {
    padding: var(--space-2) var(--space-3) var(--space-4);
    overflow-x: hidden; /* Prevent horizontal overflow */
  }
}

/* Extra small devices (360px and below) */
@media (width <= 360px) {
  .cardAuth {
    padding: 0;
    overflow-x: hidden;
  }

  .cardBodyAuth {
    padding: var(--space-2) var(--space-2) var(--space-3);
    overflow-x: hidden;
  }

  .cardHeaderAuth {
    padding-top: var(--space-3);
    padding-bottom: var(--space-2);
  }

  .cardHeaderAuth .cardTitle {
    font-size: var(--font-size-base);
  }
}
