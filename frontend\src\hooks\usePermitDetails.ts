import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

import { getApplicationById, downloadPermit } from '../services/applicationService';
import { useToast } from '../shared/hooks/useToast';
import { usePermitStatusPolling } from './usePermitStatusPolling';
import { logger } from '../utils/logger';

/**
 * A custom hook to manage all the business logic and state for the PermitDetailsPage.
 * It encapsulates data fetching, state management, and user action handlers.
 */
export const usePermitDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showToast } = useToast();

  // State for UI interactions
  const [isDownloading, setIsDownloading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [showOxxoModal, setShowOxxoModal] = useState(false);

  // The core data query for the permit details
  const {
    data: applicationData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['application', id],
    queryFn: () => getApplicationById(id!),
    enabled: !!id,
  });

  // --- ACTION HANDLERS ---

  const handleDownloadPermit = async (type: 'permiso' | 'certificado' | 'placas' | 'recomendaciones' = 'permiso') => {
    if (!id) return;
    setIsDownloading(true);
    try {
      const pdfBlob = await downloadPermit(id, type);
      const url = window.URL.createObjectURL(pdfBlob);
      const a = document.createElement('a');
      const folio = (applicationData?.application as any)?.folio || id;
      a.href = url;
      a.download = `${type}_${folio}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} descargado.`, 'success');
    } catch (err) {
      logger.error(`Error downloading ${type}:`, err);
      showToast(`No pudimos descargar tu ${type}.`, 'error');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleViewOxxoSlip = () => {
    setShowOxxoModal(true);
  };

  const handleCopyReference = () => {
    const reference = (applicationData as any)?.oxxoReference;
    if (reference) {
      navigator.clipboard.writeText(reference);
      setCopied(true);
      showToast('Referencia OXXO copiada al portapapeles.', 'success');
      setTimeout(() => setCopied(false), 2000);
    } else {
      showToast('Referencia OXXO no disponible.', 'error');
    }
  };

  const handleRenewClick = () => {
    // This logic can be expanded as needed
    navigate(`/permits/renew/${id}`);
  };

  // --- DERIVED STATE & VALUES ---

  const currentStatus = applicationData?.status?.currentStatus;

  // --- INTELLIGENT STATUS POLLING ---
  
  // Integrate automatic status polling for permit updates
  const { isPolling } = usePermitStatusPolling({
    applicationId: id || null,
    currentStatus: currentStatus || null,
    onStatusChange: () => {
      // Refetch data when status changes are detected
      refetch();
    },
    enabled: !!id && !!currentStatus
  });

  // The hook returns a clean interface for the UI component to consume.
  return {
    isLoading,
    isError,
    error,
    applicationData,
    currentStatus,
    state: {
      isDownloading,
      copied,
      showOxxoModal,
      isPolling,
    },
    actions: {
      handleDownloadPermit,
      handleViewOxxoSlip,
      handleCopyReference,
      handleRenewClick,
      refetch,
      closeOxxoModal: () => setShowOxxoModal(false),
    },
  };
};