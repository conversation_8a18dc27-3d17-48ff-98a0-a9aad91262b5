const orchestrator = require('./src/services/permit-generation-orchestrator.service');

async function manualPdfGeneration() {
  try {
    console.log('Manually triggering PDF generation...\n');
    
    const permitIds = [14, 36];
    
    for (const permitId of permitIds) {
      console.log(`Generating PDFs for permit ${permitId}...`);
      
      try {
        // Use the orchestrator service to generate the permit
        await orchestrator.generatePermit(permitId);
        console.log(`✅ PDF generation completed for permit ${permitId}`);
      } catch (error) {
        console.error(`❌ PDF generation failed for permit ${permitId}:`, error.message);
      }
      
      console.log(''); // Empty line for readability
    }
    
    console.log('Manual PDF generation process completed.');
    
  } catch (error) {
    console.error('Error in manual PDF generation:', error.message);
  } finally {
    process.exit(0);
  }
}

manualPdfGeneration();
