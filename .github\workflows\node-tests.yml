name: Node.js CI

on:
  push:
    branches: [ main, develop, fix/redis-lockout-production ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        # Cache npm dependencies for both root and frontend
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          frontend/package-lock.json # Add this line

    - name: Install root dependencies
      run: npm ci

    - name: Install frontend dependencies
      run: npm ci --prefix frontend # Or: cd frontend && npm ci

    - name: Check for high and critical severity vulnerabilities (root)
      run: npm audit --audit-level=high

    # Optional: Audit frontend dependencies too
    # - name: Check for high and critical severity vulnerabilities (frontend)
    #   run: npm audit --audit-level=high --prefix frontend

    - name: Run ESLint (Backend & Frontend)
      run: npm run lint:js # This is your "eslint src/ && (cd frontend && npm run lint)"

    - name: Run Stylelint
      run: npm run lint:css
  test:
    name: Test
    runs-on: ubuntu-latest
    needs: lint

    # Service containers to run with the job
    services:
      # Redis service for integration tests
      redis:
        image: redis
        ports:
          - 6379:6379
        # Set health checks to wait until redis has started
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      # PostgreSQL service for database tests
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_permisos_digitales
        ports:
          - 5432:5432
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    strategy:
      matrix:
        node-version: [18.x]

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Set up test database
      run: npm run migrate:up
      env:
        PGHOST: localhost
        PGPORT: 5432
        PGUSER: postgres
        PGPASSWORD: postgres
        PGDATABASE: test_permisos_digitales
        NODE_ENV: test

    - name: Create test storage directory
      run: mkdir -p test-storage

    - name: Run all tests with coverage and check thresholds
      run: npm run test:check-coverage
      env:
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        NODE_ENV: test
        TEST_DB_HOST: localhost
        TEST_DB_PORT: 5432
        TEST_DB_USER: postgres
        TEST_DB_PASSWORD: postgres
        TEST_DB_NAME: test_permisos_digitales
        TEST_STORAGE_PATH: ./test-storage

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: coverage/

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        directory: ./coverage/
        fail_ci_if_error: false

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    # Add build steps here if needed
    - name: Verify build
      run: echo "Build verification would go here"
