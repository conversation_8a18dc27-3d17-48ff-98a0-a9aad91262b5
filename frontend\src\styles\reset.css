/*
 * Reset and Normalization
 * Modern CSS reset with best practices and accessibility considerations
 */

/* Box sizing for all elements */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Remove default margin and padding */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;
}

/* Setup body defaults */
body {
  margin: 0;
  font-family: var(--body-font-family);
  font-size: var(--body-font-size);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
  color: var(--body-color);
  background-color: var(--body-bg);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  overflow-x: hidden;
}

/* HTML5 display definitions */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
main {
  display: block;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

/* Default list styling */
ul,
ol {
  padding-left: 1.5em;
}

/* Make images and media responsive by default */
img,
video,
picture,
embed,
object,
iframe,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* Remove text decoration from links */
a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-base);
}

/* Only style text links */
a.text-link {
  color: var(--color-primary);
}

a.text-link:hover,
a.text-link:focus {
  color: var(--color-primary-dark);
}

/* Ensure buttons have a pointer cursor */
button {
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
}

/* Remove default fieldset styles */
fieldset {
  border: 0;
  padding: 0;
  margin: 0;
}

/* Responsive tables */
table {
  border-collapse: collapse;
  width: 100%;
}

/* Improve text selection */
::selection {
  background-color: var(--color-primary);
  color: var(--color-white);
  text-shadow: none;
}

/* Remove the default margin on paragraphs */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
  margin-bottom: var(--space-3);
}

/* Fix height issues and prevent horizontal overflow */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden; /* This will hide horizontal scrollbars. If content is still cut off, we know it's an internal element still trying to be too wide. */
}

/* Remove animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-delay: -1ms !important;
    animation-duration: 1ms !important;
    animation-iteration-count: 1 !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }
}
