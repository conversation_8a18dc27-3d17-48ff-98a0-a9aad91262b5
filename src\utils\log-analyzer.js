/**
 * Simple Log Analyzer Stub
 * Minimal implementation to support logger dependency
 */

const defaultLogAnalyzer = {
  analyze: (logEntry) => {
    // Simple stub implementation
    return {
      level: logEntry.level || 'info',
      timestamp: logEntry.timestamp || new Date().toISOString(),
      message: logEntry.message || '',
      metadata: logEntry.metadata || {}
    };
  },
  
  categorize: (logEntry) => {
    return {
      category: 'general',
      severity: logEntry.level || 'info',
      tags: []
    };
  },
  
  processLogEvent: (logEntry) => {
    // Stub implementation - just return the entry
    return logEntry;
  }
};

module.exports = {
  defaultLogAnalyzer
};