# Design System Migration Guide

## Overview
This guide helps you gradually fix UI inconsistencies in the Permisos Digitales frontend without a complete rewrite.

## Quick Start

1. **Visit the Design System Demo**: Navigate to `/design-system` to see all standardized components.

2. **Import the Design System CSS**: Add this to your main CSS file:
   ```css
   @import './styles/design-system.css';
   ```

## Common Issues and Fixes

### 1. Button Inconsistencies
**Problem**: Different button styles across pages (filled vs outlined, different colors)

**Fix**: Replace custom button styles with standard classes:
```jsx
// Before
<button style={{backgroundColor: '#B5384D'}}>Iniciar Sesión</button>
<button className="custom-button">Registrarse</button>

// After
<button className="btn btn-primary">Iniciar Sesión</button>
<button className="btn btn-secondary">Registrarse</button>
```

### 2. Color Variations
**Problem**: Multiple shades of red (#B5384D, #B71C1C, etc.)

**Fix**: Use CSS variables:
```css
/* Before */
.header { background-color: #B5384D; }
.alert { color: #B71C1C; }

/* After */
.header { background-color: var(--color-primary); }
.alert { color: var(--color-error); }
```

### 3. Input Field Styling
**Problem**: Inconsistent input field appearance

**Fix**: Apply form-control class:
```jsx
// Before
<input type="text" className="custom-input" />

// After
<input type="text" className="form-control" />
```

### 4. Mobile Navigation Z-Index
**Problem**: Navigation menu appears behind other elements

**Fix**: Use proper z-index variables:
```css
/* Before */
.mobile-nav { z-index: 999; }

/* After */
.mobile-nav { z-index: var(--z-index-sticky); }
```

### 5. Footer Positioning
**Problem**: Footer overlaps content or appears in wrong position

**Fix**: Implement sticky footer:
```css
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

.footer {
  margin-top: auto;
}
```

### 6. Spacing Inconsistencies
**Problem**: Random padding/margin values

**Fix**: Use spacing variables:
```css
/* Before */
.card { padding: 20px; margin-bottom: 15px; }

/* After */
.card { 
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}
```

## Step-by-Step Migration Process

### Phase 1: Foundation (1-2 hours)
1. Import `design-system.css` in your main CSS file
2. Fix z-index issues in mobile navigation
3. Standardize all primary action buttons

### Phase 2: Forms (2-3 hours)
1. Apply `form-control` class to all inputs
2. Fix credit card form styling
3. Standardize error message display

### Phase 3: Layout (2-3 hours)
1. Fix footer positioning across all pages
2. Standardize card components
3. Apply consistent spacing

### Phase 4: Colors & Typography (1-2 hours)
1. Replace hardcoded colors with variables
2. Apply consistent heading sizes
3. Fix link colors

### Phase 5: Mobile Optimization (2-3 hours)
1. Fix responsive breakpoints
2. Improve touch targets
3. Test on actual devices

## Testing Checklist

- [ ] All buttons look consistent
- [ ] Forms have same styling
- [ ] Mobile navigation works properly
- [ ] Footer stays at bottom
- [ ] Colors match design system
- [ ] Spacing is consistent
- [ ] Mobile experience is smooth

## Component Reference

### Buttons
```jsx
<button className="btn btn-primary">Primary Action</button>
<button className="btn btn-secondary">Secondary Action</button>
<button className="btn btn-danger">Danger Action</button>
<button className="btn btn-primary btn-small">Small Button</button>
<button className="btn btn-primary btn-large">Large Button</button>
```

### Forms
```jsx
<div className="form-group">
  <label>Email</label>
  <input type="email" className="form-control" />
  <div className="form-error">Error message</div>
</div>
```

### Cards
```jsx
<div className="card">
  <h3>Card Title</h3>
  <p>Card content</p>
</div>

<div className="card card-raised">
  <h3>Elevated Card</h3>
  <p>This card has more shadow</p>
</div>
```

## Tips for Success

1. **Don't try to fix everything at once** - Work page by page
2. **Test on mobile** - Many issues are mobile-specific
3. **Use the design system demo** - It's your reference
4. **Keep the old code** - Comment it out instead of deleting
5. **Document changes** - Note what you fixed and why

## Common Pitfalls to Avoid

- Don't mix inline styles with CSS classes
- Don't create new color variations
- Don't use arbitrary spacing values
- Don't forget to test mobile views
- Don't skip the hover/focus states

## Need Help?

- Check `/design-system` for live examples
- Look at the CSS variables in `design-system.css`
- Search for existing usage in the codebase
- Test changes in browser DevTools first