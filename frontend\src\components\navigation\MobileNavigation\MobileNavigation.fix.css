/* 
 * Mobile Navigation Fix
 * This file demonstrates how to fix the overlay issues
 * Apply these changes to your existing MobileNavigation.module.css
 */

/* Fix for navigation overlay appearing behind content */
.mobileNav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-modal-backdrop); /* Use proper z-index */
  display: flex;
  transition: var(--transition-base);
}

/* Navigation menu panel */
.navPanel {
  background-color: var(--color-surface);
  width: 280px;
  height: 100%;
  box-shadow: var(--shadow-xl);
  overflow-y: auto;
  transform: translateX(-100%);
  transition: transform var(--transition-base);
  z-index: var(--z-index-modal); /* Higher than backdrop */
}

.navPanel.open {
  transform: translateX(0);
}

/* Menu items */
.navItem {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--color-text-primary);
  text-decoration: none;
  border-bottom: 1px solid var(--color-border);
  transition: var(--transition-base);
}

.navItem:hover {
  background-color: var(--color-background-alt);
  color: var(--color-primary);
}

.navItem.active {
  background-color: var(--color-primary-lighter);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

/* Close button */
.closeButton {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: none;
  border: none;
  padding: var(--spacing-sm);
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: var(--transition-base);
}

.closeButton:hover {
  color: var(--color-text-primary);
  background-color: var(--color-background-alt);
  border-radius: var(--radius-md);
}

/* Prevent body scroll when menu is open */
body.menu-open {
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .navPanel {
    width: 100%;
  }
}