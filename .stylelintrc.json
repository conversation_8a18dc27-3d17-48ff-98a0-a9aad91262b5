{"extends": "stylelint-config-standard", "rules": {"selector-class-pattern": "^[a-z]+([A-Z][a-z0-9]+)*(-[a-z0-9]+)*$", "custom-property-pattern": null, "keyframes-name-pattern": "^([a-z]+([A-Z][a-z0-9]+)*(-[a-z0-9]+)*$)|(^[a-z][a-z0-9]*(-[a-z0-9]+)*$)", "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "local", "export"]}], "at-rule-empty-line-before": ["always", {"except": ["blockless-after-same-name-blockless", "first-nested"], "ignore": ["after-comment"], "ignoreAtRules": ["else"]}], "media-query-no-invalid": null, "no-descending-specificity": null, "font-family-no-missing-generic-family-keyword": null, "declaration-property-value-no-unknown": true, "block-no-empty": true, "value-keyword-case": ["lower", {"ignoreKeywords": ["currentColor", "inherit", "initial", "unset"]}]}, "ignoreFiles": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/coverage/**"]}