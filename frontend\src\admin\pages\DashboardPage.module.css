/* Dashboard Page Styles */
/* Container styling now handled by ResponsiveContainer component */

/* Page Header */
.pageHeader {
  margin-bottom: 2rem;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem;
}

.pageSubtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Stats Overview */
.statsOverview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background-color: white;
  border: 1px solid #e9ecef;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.statHeader {
  margin-bottom: 1rem;
}

.statTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #343a40;
  margin: 0;
}

.statContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.statValue {
  font-size: 2.5rem;
  font-weight: 700;
  color: #343a40;
}

.statIcon {
  font-size: 2.5rem;
  color: #a72b31;
  opacity: 0.8;
}

.statFooter {
  margin-top: auto;
}

.statLink {
  display: inline-block;
  color: #0d6efd;
  text-decoration: none;
  font-size: 0.875rem;
}

.statLink:hover {
  text-decoration: underline;
}

.statDetail {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.statDetailLabel {
  color: #6c757d;
}

.statDetailValue {
  font-weight: 600;
  color: #343a40;
}

/* Status Section */
.statusSection {
  margin-bottom: 2rem;
}

.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.statusCard {
  background-color: white;
  border: 1px solid #e9ecef;
  padding: 1rem;
  display: flex;
  align-items: center;
}

.statusIcon {
  font-size: 1.5rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconPending {
  color: #ffc107;
}

.iconSubmitted {
  color: #0d6efd;
}

.iconVerified {
  color: #198754;
}

.iconRejected {
  color: #a72b31;
}

.iconGenerated {
  color: #198754;
}

.iconCompleted {
  color: #198754;
}

.iconCancelled {
  color: #6c757d;
}

.iconDefault {
  color: #6c757d;
}

.statusContent {
  flex: 1;
}

.statusCount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #343a40;
  line-height: 1.2;
}

.statusName {
  font-size: 0.75rem;
  color: #6c757d;
}

.emptyStatusMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: white;
  border: 1px solid #e9ecef;
  text-align: center;
}

.emptyStatusIcon {
  font-size: 2rem;
  color: #6c757d;
  margin-bottom: 1rem;
}

/* Quick Links */
.quickLinks {
  margin-bottom: 2rem;
}

.linksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.linkCard {
  background-color: white;
  border: 1px solid #e9ecef;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #343a40;
  transition: all 0.15s ease;
}

.linkCard:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgb(0 0 0 / 5%);
}

.linkIcon {
  font-size: 1.5rem;
  color: #a72b31;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.linkText {
  font-size: 1rem;
  font-weight: 500;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgb(0 0 0 / 10%);
  border-radius: 50%;
  border-top-color: #a72b31;
  animation: spin var(--animation-duration-base) ease-in-out infinite;
  margin-bottom: 1rem;
}

.errorIcon {
  font-size: 3rem;
  color: #a72b31;
  margin-bottom: 1rem;
}

.retryButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #a72b31;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease;
}

.retryButton:hover {
  background-color: #852d2d;
}

/* Responsive Styles */
@media (width <= 768px) {
  .pageHeader {
    margin-bottom: 1.5rem;
  }

  .pageTitle {
    font-size: 1.5rem;
  }

  .statsOverview {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .statusGrid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.75rem;
  }

  .linksGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .statCard,
  .statusCard,
  .linkCard {
    padding: 1.25rem;
  }
}

@media (width <= 480px) {
  .pageHeader {
    margin-bottom: 1rem;
  }

  .pageTitle {
    font-size: 1.25rem;
  }

  .pageSubtitle {
    font-size: 0.875rem;
  }

  .sectionTitle {
    font-size: 1.1rem;
  }

  .statCard,
  .statusCard,
  .linkCard {
    padding: 1rem;
  }

  .statValue {
    font-size: 2rem;
  }

  .statIcon {
    font-size: 2rem;
  }

  .statusCount {
    font-size: 1.25rem;
  }

  .statusName {
    font-size: 0.7rem;
  }

  .linkText {
    font-size: 0.9rem;
  }

  .statusGrid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.5rem;
  }
}

@media (width <= 360px) {
  .statCard,
  .statusCard,
  .linkCard {
    padding: 0.75rem;
  }

  .statusGrid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .retryButton {
    width: 100%;
    max-width: 200px;
  }
}
