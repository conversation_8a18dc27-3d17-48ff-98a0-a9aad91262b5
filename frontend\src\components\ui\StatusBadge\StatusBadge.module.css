/* StatusBadge Component Styles */
.statusBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  border: 1px solid;
  transition: all 0.2s ease;
}

/* Icon styling */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 14px;
  height: 14px;
}

.icon svg {
  width: 100%;
  height: 100%;
}

/* Status: Waiting for verification */
.statusWaiting {
  background-color: var(--color-info-lightest, #e0f7fa);
  color: var(--color-info-dark, #0bacce);
  border-color: var(--color-info, #0dcaf0);
}

/* Status: Action needed from user */
.statusActionNeeded {
  background-color: var(--color-warning-lightest, #fffde7);
  color: var(--color-warning-dark, #d97706);
  border-color: var(--color-warning, #ffc107);
}

/* Status: Approved or completed */
.statusApproved {
  background-color: var(--color-success-lightest, #e8f5e9);
  color: var(--color-success-dark, #2e7d32);
  border-color: var(--color-success, #4caf50);
}

/* Status: Pending or processing */
.statusPending {
  background-color: var(--color-primary-lightest, #f8e9ea);
  color: var(--color-primary-dark, #852023);
  border-color: var(--color-primary, #a72b31);
}

/* Status: Rejected or error */
.statusRejected {
  background-color: var(--color-danger-lightest, #ffebee);
  color: var(--color-danger-dark, #c62828);
  border-color: var(--color-danger, #dc3545);
}

/* Status: Warning or expiring */
.statusWarning {
  background-color: var(--color-warning-lightest, #fffde7);
  color: var(--color-warning-dark, #d97706);
  border-color: var(--color-warning, #ffc107);
}

/* Large status badge variant */
.statusBadgeLarge {
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
}

/* Hover effect for all status badges */
.statusBadge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Media query for smaller screens */
@media (width <= 576px) {
  .statusBadge {
    padding: 3px 10px;
    font-size: 11px;
  }

  .statusBadgeLarge {
    padding: 4px 14px;
    font-size: 13px;
  }
}
