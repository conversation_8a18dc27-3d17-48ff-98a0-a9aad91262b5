/**
 * Simple Login Test for Auth Routes
 * This file contains a minimal test to debug issues with the login endpoint
 */
const request = require('supertest');
const express = require('express');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');

// Mock Redis client to prevent open handles
jest.mock('../../utils/redis-client', () => ({
  status: 'ready',
  incr: jest.fn().mockResolvedValue(1),
  expire: jest.fn().mockResolvedValue('OK'),
  set: jest.fn().mockResolvedValue('OK'),
  get: jest.fn().mockResolvedValue(null),
  exists: jest.fn().mockResolvedValue(0),
  ttl: jest.fn().mockResolvedValue(0),
  del: jest.fn().mockResolvedValue(1),
  quit: jest.fn().mockResolvedValue('OK')
}));

// Mock DB
jest.mock('../../db', () => ({
  query: jest.fn()
}));

// Mock enhanced logger
jest.mock('../../utils/logger', () => ({
  logger: {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock password utilities
jest.mock('../../utils/password-utils', () => ({
  hashPassword: jest.fn().mockImplementation(async (password) => {
    return '$2b$10$abcdefghijklmnopqrstuvwxyz0123456789';
  }),
  verifyPassword: jest.fn().mockImplementation(async (password, hash) => {
    return password === 'correct-password';
  })
}));

// Mock CSRF middleware
jest.mock('../../middleware/csrf.middleware', () => ({
  csrfProtection: (req, res, next) => {
    req.csrfToken = () => 'test-csrf-token';
    next();
  },
  handleCsrfError: (err, req, res, next) => {
    if (err.code === 'EBADCSRFTOKEN') {
      return res.status(403).json({ message: 'Invalid CSRF token' });
    }
    next(err);
  }
}));

// Mock auth security service
jest.mock('../../services/auth-security.service', () => ({
  recordFailedAttempt: jest.fn().mockResolvedValue({ attempts: 1, lockedUntil: null }),
  resetAttempts: jest.fn().mockResolvedValue(true),
  checkLockStatus: jest.fn().mockResolvedValue({ locked: false })
}));

// Mock security service
jest.mock('../../services/security.service', () => ({
  isRateLimitExceeded: jest.fn().mockResolvedValue(false),
  logActivity: jest.fn().mockResolvedValue(true)
}));

// Import dependencies after mocking
const { verifyPassword } = require('../../utils/password-utils');
const db = require('../../db');
const authSecurity = require('../../services/auth-security.service');

describe('Auth Login Test', () => {
  let app;
  let server;

  // Set a longer timeout
  jest.setTimeout(10000);

  // Close the server after all tests
  afterAll(done => {
    if (server) {
      server.close(() => {
        done();
      });
    } else {
      done();
    }
  });

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Close any previous server instance
    if (server) {
      server.close();
    }

    // Create a new Express app
    app = express();

    // Add middleware
    app.use(bodyParser.json());
    app.use(bodyParser.urlencoded({ extended: true }));
    app.use(cookieParser('test-secret'));

    // Create a simple in-memory session store for testing
    const MemoryStore = session.MemoryStore;
    const sessionStore = new MemoryStore();

    // Add session middleware with in-memory store for testing
    app.use(session({
      store: sessionStore,
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false,
      cookie: { secure: false }
    }));

    // Make session operations synchronous for testing
    app.use((req, res, next) => {
      // Store the original methods
      const originalRegenerate = req.session.regenerate;
      const originalSave = req.session.save;
      const originalDestroy = req.session.destroy;

      // Override regenerate to be synchronous
      req.session.regenerate = function(callback) {
        Object.assign(req.session, { cookie: req.session.cookie });
        if (callback) callback(null);
      };

      // Override save to be synchronous
      req.session.save = function(callback) {
        if (callback) callback(null);
      };

      // Override destroy to be synchronous
      req.session.destroy = function(callback) {
        req.session = null;
        if (callback) callback(null);
      };

      next();
    });

    // Create a simple login endpoint for testing
    app.post('/api/auth/login', (req, res) => {
      const { email, password } = req.body;

      // Check if password is correct
      if (password !== 'correct-password') {
        return res.status(401).json({
          success: false,
          message: 'Invalid email or password'
        });
      }

      // Set session data
      req.session.userId = 1;
      req.session.userEmail = email;
      req.session.userName = 'Test User';
      req.session.accountType = 'client';

      // Return success response
      return res.status(200).json({
        success: true,
        data: {
          user: {
            id: 1,
            email,
            first_name: 'Test',
            last_name: 'User',
            accountType: 'client'
          }
        }
      });
    });

    // Start the server on a random port
    server = app.listen(0);
  });

  afterEach(done => {
    // Close the server after each test
    if (server) {
      server.close(() => {
        done();
      });
    } else {
      done();
    }
  });

  it('should login successfully with correct credentials', done => {
    const credentials = {
      email: '<EMAIL>',
      password: 'correct-password'
    };

    request(app)
      .post('/api/auth/login')
      .set('X-CSRF-Token', 'test-csrf-token')
      .send(credentials)
      .expect(200)
      .end((err, res) => {
        if (err) return done(err);
        expect(res.body).toHaveProperty('success', true);
        expect(res.body).toHaveProperty('data');
        expect(res.body.data).toHaveProperty('user');
        expect(res.body.data.user).toHaveProperty('id', 1);
        done();
      });
  });

  it('should return 401 for incorrect password', done => {
    const credentials = {
      email: '<EMAIL>',
      password: 'wrong-password'
    };

    request(app)
      .post('/api/auth/login')
      .set('X-CSRF-Token', 'test-csrf-token')
      .send(credentials)
      .expect(401)
      .end((err, res) => {
        if (err) return done(err);
        expect(res.body).toHaveProperty('success', false);
        expect(res.body).toHaveProperty('message');
        expect(res.body.message).toContain('Invalid email or password');
        done();
      });
  });
});
