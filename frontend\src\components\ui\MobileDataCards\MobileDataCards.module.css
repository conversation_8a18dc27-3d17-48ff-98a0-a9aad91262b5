/* Mobile Data Cards - Touch-optimized card layout for tables */

.cardList {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: 0;
  margin: 0;
}

/* Individual card with subtle depth */
.dataCard {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
  
  /* Touch feedback */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.dataCard:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

/* Card header with primary info */
.cardHeader {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-neutral-100);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-3);
}

.cardTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
  margin: 0;
  line-height: 1.3;
}

.cardSubtitle {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  margin-top: var(--space-1);
}

/* Status badge */
.statusBadge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--border-radius-pill);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.statusBadge.success {
  background: var(--status-success-bg);
  color: var(--status-success);
}

.statusBadge.warning {
  background: var(--status-warning-bg);
  color: var(--status-warning);
}

.statusBadge.error {
  background: var(--status-critical-bg);
  color: var(--status-critical);
}

.statusBadge.info {
  background: var(--status-info-bg);
  color: var(--status-info);
}

/* Card body with data rows */
.cardBody {
  padding: var(--space-4);
}

.dataRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  min-height: 44px; /* Touch target */
}

.dataRow:not(:last-child) {
  border-bottom: 1px solid var(--color-neutral-100);
}

.dataLabel {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  font-weight: var(--font-weight-medium);
}

.dataValue {
  font-size: var(--font-size-base);
  color: var(--color-neutral-900);
  text-align: right;
  font-weight: var(--font-weight-medium);
}

/* Swipe actions container */
.swipeActions {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 200px;
  display: flex;
  align-items: stretch;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.dataCard.swiping .swipeActions {
  transform: translateX(0);
}

.swipeAction {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.swipeAction:active {
  opacity: 0.8;
}

.swipeAction.edit {
  background: var(--color-info);
}

.swipeAction.delete {
  background: var(--color-danger);
}

/* Quick actions footer */
.cardFooter {
  padding: var(--space-3) var(--space-4);
  background: var(--color-neutral-100);
  display: flex;
  gap: var(--space-2);
  border-top: 1px solid var(--color-neutral-200);
}

.actionButton {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-neutral-300);
  background: var(--color-white);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-700);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  min-height: 40px;
}

.actionButton:hover {
  background: var(--color-neutral-100);
  border-color: var(--color-neutral-400);
}

.actionButton:active {
  transform: scale(0.98);
}

.actionButton.primary {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.actionButton.primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

/* Expandable details section */
.expandableSection {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.dataCard.expanded .expandableSection {
  max-height: 500px; /* Adjust based on content */
  border-top: 1px solid var(--color-neutral-200);
}

.expandableContent {
  padding: var(--space-4);
}

.expandToggle {
  width: 100%;
  padding: var(--space-3);
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  transition: all 0.2s ease;
}

.expandToggle:hover {
  background: var(--color-neutral-100);
}

.expandToggle svg {
  transition: transform 0.3s ease;
}

.dataCard.expanded .expandToggle svg {
  transform: rotate(180deg);
}

/* Loading state */
.skeletonCard {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--color-neutral-200);
}

.skeletonLine {
  height: 16px;
  background: linear-gradient(
    90deg,
    var(--color-neutral-200) 25%,
    var(--color-neutral-100) 50%,
    var(--color-neutral-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--border-radius);
  margin-bottom: var(--space-3);
}

.skeletonLine:last-child {
  margin-bottom: 0;
}

.skeletonLine.short {
  width: 60%;
}

.skeletonLine.medium {
  width: 80%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  to {
    background-position: 200% 0;
  }
}

/* Empty state */
.emptyState {
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.emptyIcon {
  font-size: 3rem;
  color: var(--color-neutral-400);
  margin-bottom: var(--space-4);
}

.emptyTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin: 0 0 var(--space-2);
}

.emptyDescription {
  font-size: var(--font-size-base);
  color: var(--color-neutral-600);
  margin: 0 0 var(--space-4);
}

/* Pull to refresh indicator */
.pullToRefresh {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: var(--color-white);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.pullToRefresh.visible {
  top: 20px;
}

.pullToRefresh.refreshing {
  animation: spin var(--animation-duration-base) linear infinite;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  /* Switch to table view on larger screens */
  .cardList {
    display: none;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dataCard {
    background: var(--color-neutral-800);
    border-color: var(--color-neutral-700);
  }
  
  .cardHeader {
    border-bottom-color: var(--color-neutral-700);
  }
  
  .cardTitle {
    color: var(--color-white);
  }
  
  .dataRow {
    border-bottom-color: var(--color-neutral-700);
  }
  
  .dataValue {
    color: var(--color-neutral-100);
  }
  
  .cardFooter {
    background: var(--color-neutral-900);
    border-top-color: var(--color-neutral-700);
  }
  
  .actionButton {
    background: var(--color-neutral-800);
    border-color: var(--color-neutral-600);
    color: var(--color-neutral-200);
}
}
}
}
}
