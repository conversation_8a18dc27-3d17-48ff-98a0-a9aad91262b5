# Permisos Digitales - Project Information

## Production Deployment Commands

### Lint and Type Check
```bash
npm run lint
npm run typecheck
```

### Build for Production
```bash
npm run build
# Note: If TypeScript errors occur, use: npx vite build
```

### Test Commands
```bash
npm test
npm run test:coverage
```

## Key Service Names (Container Registry)
- database
- redis
- stripePayment
- authService
- emailService
- pdfQueue
- paymentVelocity
- alertService
- queueMonitor
- paymentMonitoring

## Production Server Info
- Host: **************
- User: ubuntu
- Key: docs/permisos-digitales-fresh.pem
- Backend Directory: /home/<USER>/permisos-backend-deploy
- PM2 Process Name: permisos-api

## Backend Deployment

### Deploy Backend Changes
```bash
# Copy files to server
scp -i docs/permisos-digitales-fresh.pem src/path/to/file.js ubuntu@**************:/home/<USER>/permisos-backend-deploy/src/path/to/

# Restart backend
ssh -i docs/permisos-digitales-fresh.pem ubuntu@************** "pm2 restart permisos-api"
```

### Check Backend Status
```bash
ssh -i docs/permisos-digitales-fresh.pem ubuntu@************** "pm2 status"
ssh -i docs/permisos-digitales-fresh.pem ubuntu@************** "pm2 logs permisos-api --lines 50"
```

## Frontend Deployment

### Build and Deploy Frontend
```bash
cd frontend
npm run build  # or npx vite build if TypeScript errors
aws s3 sync dist/ s3://permisos-digitales-frontend-east --delete --cache-control "public, max-age=31536000, immutable" --exclude "*.html" --exclude "*.json"
aws s3 sync dist/ s3://permisos-digitales-frontend-east --cache-control "no-cache, no-store, must-revalidate" --content-type "text/html" --include "*.html"
aws cloudfront create-invalidation --distribution-id ECOBED0P176S0 --paths "/*"
```

## API Endpoints
- Production API: https://api.permisosdigitales.com.mx
- Health Check: https://api.permisosdigitales.com.mx/health
- Frontend: https://permisosdigitales.com.mx

## Recent Changes (2025-06-29)
1. Added OXXO processing time notification (1-4 hours) on confirmation page
2. Implemented permit ready email notifications with 48-hour download links
3. Set up CloudFront redirect from .com to .com.mx domains
4. Fixed backend nginx configuration (was missing proxy setup)

## Known Issues (RESOLVED)
- ~~Health endpoint shows "critical" status for "startupSequence" but API is functional~~ (Fixed 2025-06-29)
- Express rate limit warning about trust proxy setting (non-critical)
- AWS SDK v2 deprecation warnings (migration to v3 needed eventually)

## Recent Fixes (2025-06-29)
### Health Monitor False Positive Fix
Fixed false positive "critical" status in health monitoring that was causing unnecessary PM2 restarts:
- Modified `src/monitoring/health-monitor.js` startupSequence check
- Changed behavior: After 5-minute startup window, always return healthy status
- Changed criticality from `critical: true` to `critical: false`
- Result: Prevents unnecessary application restarts while individual service health is still monitored
- PM2 restart command: `sudo pm2 kill && sudo pm2 start ecosystem.production.config.js`