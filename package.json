{"name": "permisos-digitales", "version": "1.0.0", "description": "Digital permit application and management system", "main": "src/server.js", "scripts": {"start": "node src/server.js", "start:v2": "node src/server-v2.js", "start:dev": "node src/server-dev.js", "dev": "nodemon src/server.js", "dev:v2": "nodemon src/server-v2.js", "dev:server": "nodemon src/server-dev.js", "dev:simple": "nodemon src/server-simple.js", "dev:minimal": "nodemon src/server-minimal.js", "dev:check": "node src/server-dev.js --check", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "format": "prettier --write \"src/**/*.js\"", "db:validate": "node src/scripts/database-validation.js", "db:rebuild": "PGPASSWORD=password psql -U permisos_user -d permisos_digitales_v2 -f src/db/schema/master_schema.sql", "db:backup": "PGPASSWORD=password pg_dump -U permisos_user permisos_digitales_v2 > backups/backup_$(date +%Y%m%d_%H%M%S).sql", "db:dump-current": "PGPASSWORD=password pg_dump --schema-only --no-owner --no-privileges -U permisos_user permisos_digitales_v2 > schema/current_schema_$(date +%Y%m%d).sql", "secrets:migrate": "node aws/secrets-manager/scripts/quick-migrate-secrets.js", "secrets:migrate:interactive": "node aws/secrets-manager/scripts/migrate-env-to-secrets.js", "secrets:setup": "bash aws/secrets-manager/scripts/setup-secrets-manager.sh", "health:check": "curl http://localhost:3001/health | jq", "quickfix:apply": "cp src/config/stripe-quickfix.js src/config/stripe.js && echo 'Quick fix applied!'"}, "keywords": ["permits", "government", "digital"], "author": "Permisos Digitales Team", "license": "UNLICENSED", "dependencies": {"@aws-sdk/client-s3": "^3.835.0", "@aws-sdk/s3-request-presigner": "^3.835.0", "@dr.pogodin/csurf": "^1.14.0", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "bull": "^4.16.5", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-http-context": "^1.2.4", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "ioredis": "^5.6.1", "joi": "^17.13.3", "memorystore": "^1.6.7", "multer": "^2.0.1", "node-cron": "^4.0.7", "node-pg-migrate": "^8.0.0", "nodemailer": "^7.0.3", "pdfkit": "^0.17.1", "pg": "^8.15.6", "prom-client": "^15.1.3", "puppeteer-core": "^22.10.0", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.4.2", "supertest": "^7.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}