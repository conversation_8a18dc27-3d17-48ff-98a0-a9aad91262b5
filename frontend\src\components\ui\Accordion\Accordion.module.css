/* Accordion Component Styles */
.accordionContainer {
  width: 100%;
  margin-bottom: var(--space-4);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow-sm);
  background-color: var(--color-white);
}

.accordionItem {
  border-bottom: 1px solid var(--color-neutral-200);
}

.accordionItem:last-child {
  border-bottom: none;
}

.accordionButton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background-color: var(--color-white);
  border: none;
  cursor: pointer;
  text-align: left;
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-800);
  transition: background-color 0.2s ease;
  min-height: 44px; /* WCAG-compliant touch target */
}

.accordionButton:hover {
  background-color: var(--color-neutral-50);
}

.accordionTitle {
  margin-right: var(--space-3);
  flex: 1;
  white-space: normal;
  word-break: break-word;
}

.accordionIcon {
  font-size: 1.2rem;
  color: var(--color-neutral-500);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.accordionButton[aria-expanded='true'] .accordionIcon {
  color: var(--color-primary);
  transform: rotate(180deg);
}

.accordionPanel {
  max-height: 0;
  overflow: hidden;
  transition:
    max-height 0.3s ease-out,
    padding 0.3s ease-out;
  background-color: var(--color-neutral-50);
}

.accordionPanelOpen {
  max-height: 500px; /* Adjust as needed */
  transition:
    max-height 0.4s ease-in,
    padding 0.3s ease-in;
  padding-bottom: var(--space-3);
  overflow-y: auto;
}

.accordionContent {
  padding: var(--space-3) var(--space-4);
  font-size: 0.95rem;
  color: var(--color-neutral-700);
  line-height: 1.6;
  white-space: normal;
  word-break: break-word;
}

/* Responsive Styles */
@media (width <= 768px) {
  .accordionButton {
    padding: var(--space-3);
  }

  .accordionContent {
    padding: var(--space-3);
  }
}

@media (width <= 480px) {
  .accordionButton {
    padding: var(--space-2) var(--space-3);
    font-size: 0.95rem;
  }

  .accordionContent {
    padding: var(--space-2) var(--space-3);
    font-size: 0.9rem;
  }
}
