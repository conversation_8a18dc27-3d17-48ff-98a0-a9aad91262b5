/*
 * Modern Link Design System
 * Consistent link styles across the application
 */

/*
 * Base Link Styles
 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}

/* Inline text links - exclude button containers */
a.text-link {
  display: inline-block;
  font-weight: 500;
  padding: 2px 0;
  position: relative;
}

/* Hover state for text links */
a.text-link:hover {
  color: var(--color-primary-dark);
}

/* Animated underline for text links */
a.text-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a.text-link:hover::after {
  width: 100%;
}

/* Button wrapper links - ensure no underlines */
.btn-wrapper,
.button-link {
  text-decoration: none !important;
  display: inline-block;
}

.btn-wrapper::after,
.button-link::after {
  display: none !important;
}

/* Focus state for accessibility */
a:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 4px;
  border-radius: 2px;
}

/*
 * Navigation Links
 */
.nav-link {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--color-neutral-700);
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
  min-height: 44px;
}

.nav-link:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-lightest);
}

.nav-link.active {
  color: var(--color-primary);
  font-weight: 600;
  background-color: var(--color-primary-lightest);
}

/* Navigation link indicator */
.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: 0 2px 2px 0;
}

/*
 * Card Links - Make entire cards clickable
 */
.card-link {
  display: block;
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

.card-link::after {
  display: none !important; /* No underline for card links */
}

.card-link:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-link:hover .card-title {
  color: var(--color-primary);
}

/*
 * Breadcrumb Links
 */
.breadcrumb-link {
  color: var(--color-neutral-600);
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--color-primary);
  background-color: var(--color-neutral-100);
}

/*
 * Footer Links
 */
.footer-link {
  color: var(--color-neutral-600);
  font-weight: 400;
  padding: 8px 0;
  display: inline-block;
}

.footer-link:hover {
  color: var(--color-primary);
}

/*
 * External Link Indicator
 */
a[target="_blank"]:not(.btn, [class*='btn-'])::after {
  content: ' ↗';
  font-size: 0.8em;
  vertical-align: super;
  margin-left: 2px;
  opacity: 0.7;
}

/*
 * Mobile Optimizations
 */
@media (max-width: 480px) { /* breakpoint-sm */
  /* Increase touch targets for mobile */
  a:not(.btn, [class*='btn-'], [role='button']) {
    min-height: 44px;
    padding: 10px 0;
    display: inline-flex;
    align-items: center;
  }
  
  .nav-link {
    padding: 12px 16px;
    font-size: 16px;
  }
  
  /* Remove hover effects on touch devices */
  @media (hover: none) {
    a::after {
      display: none;
    }
    
    .nav-link:hover {
      background-color: transparent;
    }
  }
}