/* FailedPermitsPage.module.css */

.container {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
}

.pageHeader {
  margin-bottom: var(--spacing-xl);
}

.pageHeader h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-primary);
}

.subtitle {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
}

/* Metrics Grid */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.metricCard {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.metricCard.urgent {
  border-left: 4px solid var(--color-danger);
  background-color: var(--color-danger-bg);
}

.metricContent h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.metricValue {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--color-text-primary);
}

.metricLabel {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

/* Category Summary */
.categoryCard {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
}

.categoryCard h3 {
  font-size: 1.125rem;
  margin-bottom: var(--spacing-md);
}

.categoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.categoryItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background-secondary);
  border-radius: var(--border-radius-sm);
}

.categoryName {
  flex: 1;
  font-weight: 500;
}

.categoryCount {
  font-weight: 700;
  color: var(--color-primary);
}

/* Applications List */
.applicationsList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.applicationCard {
  border-left: 4px solid var(--color-border);
  transition: all 0.2s ease;
}

.applicationCard:hover {
  box-shadow: var(--shadow-lg);
}

/* Severity Colors */
.severityCritical {
  border-left-color: var(--color-danger) !important;
}

.severityHigh {
  border-left-color: var(--color-warning) !important;
}

.severityMedium {
  border-left-color: var(--color-info) !important;
}

.severityLow {
  border-left-color: var(--color-success) !important;
}

.severityUnknown {
  border-left-color: var(--color-border) !important;
}

/* Card Header */
.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.headerInfo h3 {
  font-size: 1.125rem;
  margin: 0;
  color: var(--color-text-primary);
}

.timeAgo {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.headerBadges {
  display: flex;
  gap: var(--spacing-sm);
}

.categoryBadge,
.severityBadge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.categoryBadge {
  background-color: var(--color-info-bg);
  color: var(--color-info);
}

.categoryTIMEOUT {
  background-color: var(--color-warning-bg);
  color: var(--color-warning);
}

.categoryAUTH_FAILURE {
  background-color: var(--color-danger-bg);
  color: var(--color-danger);
}

.categoryPORTAL_CHANGED {
  background-color: var(--color-danger-bg);
  color: var(--color-danger);
}

/* User Info */
.userInfo {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.userInfo p {
  margin: 0;
  line-height: 1.5;
}

.userInfo strong {
  color: var(--color-text-primary);
}

/* Error Details */
.errorDetails {
  background-color: var(--color-background-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-md);
}

.errorMessage {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--color-text-primary);
}

.suggestion {
  margin: 0;
  padding-left: var(--spacing-lg);
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Vehicle Info */
.vehicleInfo {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--color-background-secondary);
  border-radius: var(--border-radius-sm);
}

.vehicleLabel {
  font-weight: 600;
  color: var(--color-text-secondary);
}

.vehicleValue {
  color: var(--color-text-primary);
}

/* Actions */
.actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: var(--spacing-xxl);
}

.emptyState h3 {
  margin: var(--spacing-md) 0;
  color: var(--color-text-primary);
}

.emptyState p {
  color: var(--color-text-secondary);
}

/* Loading & Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-md);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin var(--animation-duration-base) linear infinite;
}

/* Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: var(--color-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.25rem;
}

.modalClose {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: var(--spacing-xs);
  line-height: 1;
}

.modalClose:hover {
  color: var(--color-text-primary);
}

.modalBody {
  padding: var(--spacing-lg);
}

.modalSubtext {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

.uploadSection {
  margin: var(--spacing-lg) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.uploadField {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.uploadField label {
  font-weight: 500;
  color: var(--color-text-primary);
}

.uploadField input[type="file"] {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
}

.notesField {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  resize: vertical;
  font-family: inherit;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-md);
  }

  .metricsGrid {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
  }

  .actions > * {
    width: 100%;
  }

  .headerBadges {
    flex-direction: column;
    align-items: flex-end;
  }

  .modal {
    width: 95%;
}
}
