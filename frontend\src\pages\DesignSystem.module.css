.container {
  min-height: 100vh;
  background-color: var(--color-background);
}

.header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.header h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.header p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
}

/* Navigation */
.nav {
  background-color: var(--color-surface);
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
  overflow-x: auto;
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
}

.nav a {
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  white-space: nowrap;
  transition: all var(--transition-base);
}

.nav a:hover {
  background-color: var(--color-background-alt);
  color: var(--color-primary);
}

/* Main Content */
.main {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.section {
  margin-bottom: var(--spacing-3xl);
  scroll-margin-top: 100px;
}

.section h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-primary);
}

.section h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
}

/* Color Section */
.colorGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.colorGroup {
  background-color: var(--color-surface);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.colorSwatch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  color: white;
  font-weight: var(--font-weight-medium);
}

.colorSwatch code {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  background-color: rgba(0, 0, 0, 0.2);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

/* Typography Section */
.typographyDemo {
  background-color: var(--color-surface);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.h1Demo {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

.h2Demo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
}

.h3Demo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  margin-bottom: var(--spacing-md);
}

.h4Demo {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  margin-bottom: var(--spacing-md);
}

.bodyDemo {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
}

.smallDemo {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

/* Spacing Section */
.spacingDemo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.spacingBox {
  background-color: var(--color-primary-light);
  border: 2px dashed var(--color-primary);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.spacingBox code {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--color-primary-dark);
}

/* Button Section */
.buttonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.buttonGrid > div {
  background-color: var(--color-surface);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Form Section */
.formDemo {
  background-color: var(--color-surface);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  max-width: 600px;
}

.formGroup {
  margin-bottom: var(--spacing-lg);
}

.formGroup label {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-primary);
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background-color: var(--color-background);
  transition: all var(--transition-base);
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.formError {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
}

/* Card Section */
.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.highlightCard {
  border: 2px solid var(--color-primary);
  background-color: var(--color-primary-light);
}

.statCard {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.statCard h4 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin: 0;
}

.statCard p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Badge Section */
.badgeGrid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  background-color: var(--color-surface);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

/* Navigation Demo */
.navDemo {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.navBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.navBrand {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.navBrand span {
  color: var(--color-primary);
}

.navMenu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.navMenu a {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-base);
}

.navMenu a:hover {
  color: var(--color-primary);
}

/* Feedback Section */
.feedbackGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.feedbackGrid > div {
  background-color: var(--color-surface);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

/* Mobile Demo */
.mobileDemo {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl);
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.phoneFrame {
  width: 375px;
  height: 667px;
  border: 16px solid #333;
  border-radius: 36px;
  overflow: hidden;
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-xl);
}

.mobileHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.mobileHeader span {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.mobileCard {
  margin: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-surface);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.mobileCard h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.mobileCard p {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: var(--spacing-xs) 0;
}

.mobileCard small {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
}

.mobileNav {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  background-color: var(--color-surface);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
}

.mobileNavItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
}

.mobileNavItem:hover {
  color: var(--color-primary);
  background-color: var(--color-background-alt);
}

.mobileNavItem span {
  font-size: var(--font-size-xs);
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-text-secondary);
}

.emptyState svg {
  margin: 0 auto var(--spacing-md);
  color: var(--color-text-muted);
}

.emptyState h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.emptyState p {
  margin-bottom: var(--spacing-lg);
}

/* Dialog */
.dialogBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal-backdrop);
}

.dialog {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  max-width: 400px;
  width: 90%;
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-index-modal);
}

.dialog h3 {
  margin-bottom: var(--spacing-md);
}

.dialogActions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);
}

/* Messages */
.messageSuccess,
.messageError,
.messageInfo {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.messageSuccess {
  background-color: #D1FAE5;
  color: #065F46;
  border: 1px solid #A7F3D0;
}

.messageError {
  background-color: #FEE2E2;
  color: #991B1B;
  border: 1px solid #FECACA;
}

.messageInfo {
  background-color: #DBEAFE;
  color: #1E3A8A;
  border: 1px solid #BFDBFE;
}

/* Issues List */
.issuesList {
  background-color: var(--color-surface);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.issuesList h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

.issuesList h3:first-child {
  margin-top: 0;
}

.issuesList p {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

/* Responsive */
@media (max-width: 768px) {
  .main {
    padding: var(--spacing-md);
  }

  .nav {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .phoneFrame {
    transform: scale(0.8);
  }
}