{"ETag": "E32B8XJB0EHCRG", "Distribution": {"Id": "ECOBED0P176S0", "ARN": "arn:aws:cloudfront::654722280275:distribution/ECOBED0P176S0", "Status": "Deployed", "LastModifiedTime": "2025-06-29T22:16:02.546000+00:00", "InProgressInvalidationBatches": 0, "DomainName": "d2gtd1yvnspajh.cloudfront.net", "ActiveTrustedSigners": {"Enabled": false, "Quantity": 0}, "ActiveTrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "DistributionConfig": {"CallerReference": "permisos-digitales-frontend-2025", "Aliases": {"Quantity": 4, "Items": ["permisosdigitales.com.mx", "www.permisosdigitales.com", "permisosdigitales.com", "www.permisosdigitales.com.mx"]}, "DefaultRootObject": "index.html", "Origins": {"Quantity": 1, "Items": [{"Id": "S3-permisos-digitales-frontend-east", "DomainName": "permisos-digitales-frontend-east.s3.amazonaws.com", "OriginPath": "", "CustomHeaders": {"Quantity": 0}, "S3OriginConfig": {"OriginAccessIdentity": ""}, "ConnectionAttempts": 3, "ConnectionTimeout": 10, "OriginShield": {"Enabled": false}, "OriginAccessControlId": "E1HT5A24RC88DY"}]}, "OriginGroups": {"Quantity": 0}, "DefaultCacheBehavior": {"TargetOriginId": "S3-permisos-digitales-frontend-east", "TrustedSigners": {"Enabled": false, "Quantity": 0}, "TrustedKeyGroups": {"Enabled": false, "Quantity": 0}, "ViewerProtocolPolicy": "redirect-to-https", "AllowedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"], "CachedMethods": {"Quantity": 2, "Items": ["HEAD", "GET"]}}, "SmoothStreaming": false, "Compress": true, "LambdaFunctionAssociations": {"Quantity": 0}, "FunctionAssociations": {"Quantity": 1, "Items": [{"FunctionARN": "arn:aws:cloudfront::654722280275:function/permisos-digitales-domain-redirect", "EventType": "viewer-request"}]}, "FieldLevelEncryptionId": "", "GrpcConfig": {"Enabled": false}, "ForwardedValues": {"QueryString": false, "Cookies": {"Forward": "none"}, "Headers": {"Quantity": 0}, "QueryStringCacheKeys": {"Quantity": 0}}, "MinTTL": 0, "DefaultTTL": 86400, "MaxTTL": 31536000}, "CacheBehaviors": {"Quantity": 0}, "CustomErrorResponses": {"Quantity": 2, "Items": [{"ErrorCode": 403, "ResponsePagePath": "/index.html", "ResponseCode": "200", "ErrorCachingMinTTL": 300}, {"ErrorCode": 404, "ResponsePagePath": "/index.html", "ResponseCode": "200", "ErrorCachingMinTTL": 300}]}, "Comment": "CloudFront distribution for Permisos Digitales frontend with custom domain", "Logging": {"Enabled": false, "IncludeCookies": false, "Bucket": "", "Prefix": ""}, "PriceClass": "PriceClass_100", "Enabled": true, "ViewerCertificate": {"CloudFrontDefaultCertificate": false, "ACMCertificateArn": "arn:aws:acm:us-east-1:654722280275:certificate/9719682a-b11e-4158-a874-561a94e1dfcc", "SSLSupportMethod": "sni-only", "MinimumProtocolVersion": "TLSv1.2_2021", "Certificate": "arn:aws:acm:us-east-1:654722280275:certificate/9719682a-b11e-4158-a874-561a94e1dfcc", "CertificateSource": "acm"}, "Restrictions": {"GeoRestriction": {"RestrictionType": "none", "Quantity": 0}}, "WebACLId": "", "HttpVersion": "http2", "IsIPV6Enabled": true, "ContinuousDeploymentPolicyId": "", "Staging": false}, "AliasICPRecordals": [{"CNAME": "www.permisosdigitales.com", "ICPRecordalStatus": "APPROVED"}, {"CNAME": "permisosdigitales.com", "ICPRecordalStatus": "APPROVED"}, {"CNAME": "www.permisosdigitales.com.mx", "ICPRecordalStatus": "APPROVED"}, {"CNAME": "permisosdigitales.com.mx", "ICPRecordalStatus": "APPROVED"}]}}