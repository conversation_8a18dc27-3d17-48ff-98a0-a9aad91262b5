const cors = require('cors');
const { logger } = require('../utils/logger');

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) {
      return callback(null, true);
    }

    let allowedOrigins = process.env.NODE_ENV === 'production'
      ? [
        'https://permisosdigitales.com.mx',
        'https://www.permisosdigitales.com.mx',
        'https://permisosdigitales.com',
        'https://www.permisosdigitales.com',
        'https://d2gtd1yvnspajh.cloudfront.net',
        'https://api.permisosdigitales.com.mx',
      ]
      : [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:3003',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://127.0.0.1:3002',
        'http://127.0.0.1:3003',
      ];

    // Add WSL IP addresses if in WSL environment (development only)
    if (process.env.NODE_ENV !== 'production' && process.env.WSL_ENVIRONMENT === 'true' && process.env.WSL_IP) {
      const wslIP = process.env.WSL_IP;
      allowedOrigins = allowedOrigins.concat([
        `http://${wslIP}:3000`,
        `http://${wslIP}:3001`,
        `http://${wslIP}:3002`,
        `http://${wslIP}:3003`
      ]);
    }

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, origin);
    } else {
      logger.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With', 'Cookie', 'X-Portal-Type'],
  exposedHeaders: ['Set-Cookie'],
  credentials: true,
  maxAge: 86400,
  preflightContinue: false,
  optionsSuccessStatus: 200
};

const corsMiddleware = (req, res, next) => {
  res.removeHeader('Access-Control-Allow-Origin');
  res.removeHeader('Access-Control-Allow-Methods');
  res.removeHeader('Access-Control-Allow-Headers');
  res.removeHeader('Access-Control-Allow-Credentials');
  res.removeHeader('Access-Control-Expose-Headers');

  cors(corsOptions)(req, res, next);
};

const simpleCorsMiddleware = (req, res, next) => {
  res.removeHeader('Access-Control-Allow-Origin');
  res.removeHeader('Access-Control-Allow-Methods');
  res.removeHeader('Access-Control-Allow-Headers');
  res.removeHeader('Access-Control-Allow-Credentials');
  res.removeHeader('Access-Control-Expose-Headers');

  const origin = req.headers.origin;

  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    'http://127.0.0.1:3002',
    'http://127.0.0.1:3003'
  ];

  // Add WSL IP addresses if in WSL environment
  if (process.env.WSL_ENVIRONMENT === 'true' && process.env.WSL_IP) {
    const wslIP = process.env.WSL_IP;
    allowedOrigins.push(
      `http://${wslIP}:3000`,
      `http://${wslIP}:3001`,
      `http://${wslIP}:3002`,
      `http://${wslIP}:3003`
    );
    logger.debug(`Added WSL IP to CORS allowed origins: ${wslIP}`);
  }

  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    res.header('Access-Control-Allow-Origin', '*');
  } else {
    logger.warn(`CORS blocked request from origin: ${origin}`);
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-Token, X-Portal-Type');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
};

module.exports = process.env.NODE_ENV === 'production' ? corsMiddleware : simpleCorsMiddleware;
