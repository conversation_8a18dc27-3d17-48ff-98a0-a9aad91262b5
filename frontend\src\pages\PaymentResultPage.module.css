.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: var(--space-4);
  background-color: var(--color-background);
}

.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--space-6);
  max-width: 600px;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.iconContainer {
  margin-bottom: var(--space-4);
}

.successIcon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-success-light);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-success);
}

.errorIcon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-error-light);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-error);
}

.successIcon svg,
.errorIcon svg {
  width: 40px;
  height: 40px;
}

.title {
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
  color: var(--color-text-dark);
}

.message {
  font-size: 1.125rem;
  margin-bottom: var(--space-3);
  color: var(--color-text);
  line-height: 1.5;
}

.submessage {
  font-size: 0.875rem;
  margin-bottom: var(--space-5);
  color: var(--color-text-light);
  line-height: 1.5;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  width: 100%;
  margin-top: var(--space-3);
}

.primaryButton {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--space-3) var(--space-4);
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.primaryButton:hover {
  background-color: var(--color-primary-dark);
}

.secondaryButton {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: var(--border-radius);
  padding: var(--space-3) var(--space-4);
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-decoration: none;
  text-align: center;
  width: 100%;
}

.secondaryButton:hover {
  background-color: var(--color-primary-light);
}

@media (width >= 768px) {
  .actions {
    flex-direction: row;
    justify-content: center;
  }

  .primaryButton,
  .secondaryButton {
    width: auto;
    min-width: 200px;
  }
}
