{"openapi": "3.0.0", "info": {"title": "Permisos Digitales API - Payment Verification", "description": "API documentation for the payment verification process", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "paths": {"/applications/{id}/payment-proof": {"post": {"summary": "Upload payment proof for an application", "description": "Upload an image or PDF of a payment proof for a pending application", "tags": ["Client - Applications"], "security": [{"cookieAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"paymentProof": {"type": "string", "format": "binary", "description": "Payment proof file (JPEG, PNG, or PDF)"}, "paymentReference": {"type": "string", "description": "Reference number for the payment"}}, "required": ["paymentProof"]}}}}, "responses": {"200": {"description": "Payment proof submitted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "applicationId": {"type": "integer"}, "status": {"type": "string"}}}}}}, "400": {"description": "Bad request (missing file, invalid application status)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "404": {"description": "Application not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/applications/{id}": {"get": {"summary": "Get application status with detailed next steps", "description": "Get detailed information about an application, including current status and what to do next", "tags": ["Client - Applications"], "security": [{"cookieAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Application status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"application": {"type": "object", "properties": {"id": {"type": "integer"}, "vehicleInfo": {"type": "object"}, "ownerInfo": {"type": "object"}, "dates": {"type": "object"}, "paymentReference": {"type": "string"}}}, "status": {"type": "object", "properties": {"currentStatus": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}, "displayMessage": {"type": "string"}, "nextSteps": {"type": "string"}, "allowedActions": {"type": "array", "items": {"type": "string"}}}}}}}}}, "400": {"description": "Bad request (invalid application ID)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "404": {"description": "Application not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/admin/pending-verifications": {"get": {"summary": "Get applications pending payment verification", "description": "Returns a list of applications with PROOF_SUBMITTED status for admin review", "tags": ["Admin - Payment Verification"], "security": [{"cookieAuth": []}], "responses": {"200": {"description": "List of pending verifications retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"applications": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string"}, "payment_proof_uploaded_at": {"type": "string", "format": "date-time"}, "payment_reference": {"type": "string"}, "nombre_completo": {"type": "string"}, "marca": {"type": "string"}, "linea": {"type": "string"}, "ano_modelo": {"type": "string"}, "user_email": {"type": "string"}}}}, "count": {"type": "integer"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "403": {"description": "Forbidden (not an admin)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/admin/applications/{id}/payment-proof": {"get": {"summary": "Get payment proof file for an application", "description": "Returns the payment proof file uploaded by the client", "tags": ["Admin - Payment Verification"], "security": [{"cookieAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Payment proof file", "content": {"image/jpeg": {}, "image/png": {}, "application/pdf": {}}}, "400": {"description": "Bad request (invalid application ID)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "403": {"description": "Forbidden (not an admin)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "404": {"description": "Application or payment proof not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/admin/applications/{id}/verify-payment": {"post": {"summary": "Verify payment for an application", "description": "Approve payment proof and start permit generation process", "tags": ["Admin - Payment Verification"], "security": [{"cookieAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"notes": {"type": "string", "description": "Admin notes about the payment verification"}}}}}}, "responses": {"200": {"description": "Payment verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "applicationId": {"type": "integer"}, "status": {"type": "string"}}}}}}, "400": {"description": "Bad request (invalid application ID or status)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "403": {"description": "Forbidden (not an admin)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}, "/admin/applications/{id}/reject-payment": {"post": {"summary": "Reject payment proof for an application", "description": "Reject payment proof and provide a reason for the client", "tags": ["Admin - Payment Verification"], "security": [{"cookieAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"rejectionReason": {"type": "string", "description": "Reason for rejecting the payment proof"}}, "required": ["rejectionReason"]}}}}, "responses": {"200": {"description": "Payment proof rejected successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "applicationId": {"type": "integer"}, "status": {"type": "string"}, "rejectionReason": {"type": "string"}}}}}}, "400": {"description": "Bad request (invalid application ID, status, or missing rejection reason)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "401": {"description": "Unauthorized (not logged in)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "403": {"description": "Forbidden (not an admin)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}}}}}, "components": {"securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "connect.sid"}}}}