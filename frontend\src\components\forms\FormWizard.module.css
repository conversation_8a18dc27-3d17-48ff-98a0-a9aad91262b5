/* Form Wizard Component Styles */
.wizardContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-4);
}

.wizardHeader {
  margin-bottom: var(--space-4);
}

.wizardTitle {
  font-size: 1.75rem;
  color: var(--color-primary-dark);
  margin-bottom: var(--space-2);
}

.wizardSubtitle {
  font-size: 1rem;
  color: var(--color-neutral-600);
  margin-bottom: var(--space-4);
}

.progressContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  position: relative;
}

.progressContainer::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-neutral-200);
  transform: translateY(-50%);
  z-index: 1;
}

.progressBar {
  position: absolute;
  top: 50%;
  left: 0;
  height: 2px;
  background-color: var(--color-primary);
  transform: translateY(-50%);
  z-index: 2;
  transition: width 0.3s ease;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 3;
}

.stepCircle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--color-white);
  border: 2px solid var(--color-neutral-300);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-600);
  margin-bottom: var(--space-2);
  transition: all 0.3s ease;
}

.stepLabel {
  font-size: 0.85rem;
  color: var(--color-neutral-600);
  text-align: center;
  max-width: 100px;
  transition: color 0.3s ease;
}

.stepActive .stepCircle {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.stepActive .stepLabel {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.stepCompleted .stepCircle {
  background-color: var(--color-success);
  border-color: var(--color-success);
  color: var(--color-white);
}

.stepContent {
  background-color: var(--color-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.navigationButtons {
  display: flex;
  justify-content: space-between;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
}

/* .buttonPrimary moved to global.css */

.buttonSecondary {
  background-color: var(--color-neutral-200);
  color: var(--color-neutral-800);
}

.buttonSecondary:hover {
  background-color: var(--color-neutral-300);
}

.buttonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.buttonDisabled:hover {
  background-color: var(--color-primary);
}

.buttonSuccess {
  background-color: var(--color-success);
  color: var(--color-white);
}

.buttonSuccess:hover {
  background-color: var(--color-success-dark);
}

/* Responsive styles */
@media (width <= 768px) {
  .progressContainer {
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }

  .step {
    margin: 0 var(--space-2);
    min-width: 60px;
  }

  .stepLabel {
    font-size: 0.75rem;
  }
}
