{"Version": "2012-10-17", "Statement": [{"Sid": "SecretsManagerRead", "Effect": "Allow", "Action": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "Resource": ["arn:aws:secretsmanager:us-east-1:654722280275:secret:permisos/production/*", "arn:aws:secretsmanager:us-east-1:654722280275:secret:permisos/development/*"]}, {"Sid": "SecretsManagerList", "Effect": "Allow", "Action": ["secretsmanager:ListSecrets"], "Resource": "*", "Condition": {"StringLike": {"secretsmanager:Name": "permisos/*"}}}, {"Sid": "KMSDecrypt", "Effect": "Allow", "Action": ["kms:Decrypt", "kms:DescribeKey"], "Resource": "arn:aws:kms:us-east-1:654722280275:key/*", "Condition": {"StringEquals": {"kms:ViaService": "secretsmanager.us-east-1.amazonaws.com"}}}]}