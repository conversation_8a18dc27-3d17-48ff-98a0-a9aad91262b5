/**
 * PM2 Production Configuration for Permisos Digitales
 * 
 * This file configures the production deployment with PM2
 * All sensitive credentials are loaded from AWS Secrets Manager
 * Non-sensitive configuration is set here as environment variables
 */

/**
 * IMPORTANT: Sensitive credentials are automatically loaded from AWS Secrets Manager
 * 
 * The following are loaded from AWS Secrets Manager (not set here):
 * - Database: DATABASE_URL, connection credentials
 * - Redis: REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
 * - Security: JWT_SECRET, SESSION_SECRET, INTERNAL_API_KEY
 * - Stripe: STRIPE_PRIVATE_KEY, STRIPE_PUBLIC_KEY, STRIPE_WEBHOOK_SECRET
 * - Email: EMAIL_USER, EMAIL_PASS, EMAIL_FROM
 * - Government: GOVT_USERNAME, GOVT_PASSWORD, GOVT_SITE_LOGIN_URL
 * 
 * AWS Secrets are organized under: permisos/production/[service]/[type]
 */

module.exports = {
  apps: [{
    name: 'permisos-digitales-api',
    script: './src/server.js',
    instances: 1,
    exec_mode: 'fork',
    max_memory_restart: '1G',
    
    // PM2 specific settings
    listen_timeout: 30000, // 30 seconds to start (AWS Secrets Manager needs time)
    kill_timeout: 10000,   // 10 seconds for graceful shutdown
    wait_ready: true,      // Wait for process.send('ready')
    
    // Auto-restart configuration
    autorestart: true,     // Auto restart if app crashes
    watch: false,          // Don't watch files in production
    max_restarts: 10,      // Maximum restarts within min_uptime
    min_uptime: '10s',     // App must run for 10s to be considered started
    restart_delay: 4000,   // Wait 4s before restarting
    
    // Exponential backoff restart delay
    exp_backoff_restart_delay: 100, // Start with 100ms delay
    
    // Stop restarting if app crashes too much
    stop_exit_codes: [0],  // Don't restart on clean exit
    
    // Log configuration
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    time: true,
    merge_logs: true,      // Combine logs from all instances
    
    // Log rotation
    log_type: 'json',      // JSON formatted logs
    max_size: '100M',      // Rotate when log reaches 100MB
    retain: '30',          // Keep 30 rotated logs
    compress: true,        // Compress rotated logs
    
    // Environment variables
    env: {
      // Core Configuration
      NODE_ENV: 'production',
      PORT: 3001,
      HOST: '0.0.0.0',
      
      // Application URLs
      APP_URL: 'https://api.permisosdigitales.com.mx',
      FRONTEND_URL: 'https://permisosdigitales.com.mx',
      API_URL: 'https://api.permisosdigitales.com.mx',
      
      // AWS Configuration
      AWS_REGION: 'us-east-1',
      USE_SECRETS_MANAGER: 'true',
      
      // S3 Storage
      S3_BUCKET: 'permisos-digitales-files-east',
      S3_REGION: 'us-east-1',
      STORAGE_TYPE: 's3',
      STORAGE_PROVIDER: 's3',
      
      // Email Service (SES)
      EMAIL_HOST: 'email-smtp.us-east-1.amazonaws.com',
      EMAIL_PORT: '587',
      EMAIL_PROVIDER: 'ses',
      EMAIL_ENABLED: 'true',
      
      // Database Pool Configuration
      DB_POOL_MIN: '2',
      DB_POOL_MAX: '10',
      DB_SSL: 'true',
      RDS_CA_CERT_PATH: './src/utils/rds-ca-bundle.pem',
      DB_CONNECTION_TIMEOUT: '10000',
      DB_IDLE_TIMEOUT: '30000',
      
      // Redis Configuration
      REDIS_DB: '0',
      REDIS_ENABLED: 'true',
      
      // Note: Session configuration is managed by the security service
      // using values from AWS Secrets Manager
      
      // Feature Flags
      PAYMENT_VELOCITY_ENABLED: 'true',
      EMAIL_QUEUE_ENABLED: 'true',
      PDF_GENERATION_ENABLED: 'true',
      PAYMENT_RECOVERY_ENABLED: 'true',
      PERMIT_EXPIRATION_JOB_ENABLED: 'true',
      NOTIFICATIONS_ENABLED: 'true',
      
      // Payment Velocity Limits
      PAYMENT_VELOCITY_USER_HOURLY: '10',
      PAYMENT_VELOCITY_USER_DAILY: '50',
      PAYMENT_VELOCITY_USER_MONTHLY: '500',
      PAYMENT_VELOCITY_EMAIL_HOURLY: '5',
      PAYMENT_VELOCITY_EMAIL_DAILY: '20',
      PAYMENT_VELOCITY_IP_HOURLY: '20',
      PAYMENT_VELOCITY_IP_DAILY: '100',
      PAYMENT_VELOCITY_CARD_HOURLY: '5',
      PAYMENT_VELOCITY_CARD_DAILY: '20',
      PAYMENT_VELOCITY_HIGH_VALUE_THRESHOLD: '5000',
      PAYMENT_VELOCITY_HIGH_VALUE_HOURLY: '2',
      PAYMENT_VELOCITY_HIGH_VALUE_DAILY: '5',
      PAYMENT_VELOCITY_RAPID_FIRE_WINDOW: '300',    // 5 minutes
      PAYMENT_VELOCITY_RAPID_FIRE_THRESHOLD: '3',
      PAYMENT_VELOCITY_MULTIPLE_CARDS_WINDOW: '3600', // 1 hour
      PAYMENT_VELOCITY_MULTIPLE_CARDS_THRESHOLD: '3',
      
      // Monitoring & Logging
      LOG_LEVEL: 'info',
      LOG_PRETTY: 'false',
      LOG_TO_FILE: 'true',
      MONITORING_ENABLED: 'true',
      AUDIT_LOG_ENABLED: 'true',
      
      // Note: Rate limiting is configured in the middleware
      // with default values if not specified
      
      // PDF Generation
      PUPPETEER_EXECUTABLE_PATH: '/usr/bin/chromium-browser',
      MAX_CONCURRENT_PDFS: '5',
      PDF_PROCESSOR_BATCH_SIZE: '10',
      PDF_PROCESSOR_INTERVAL: '5000',
      
      // Security
      PERMIT_EXPIRATION_WARNING_DAYS: '7,3,1',
      BCRYPT_ROUNDS: '12',
      
      // CORS (production domains)
      CORS_ORIGINS: 'https://permisosdigitales.com.mx,https://www.permisosdigitales.com.mx,https://admin.permisosdigitales.com.mx'
    }
  }],
  
  // PM2+ Monitoring (Optional - requires PM2+ account)
  // Uncomment and add your keys to enable cloud monitoring
  // pm2_plus: {
  //   enabled: true,
  //   public_key: 'YOUR_PUBLIC_KEY',
  //   secret_key: 'YOUR_SECRET_KEY',
  //   machine_name: 'permisos-prod-1'
  // }
};