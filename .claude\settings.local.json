{"permissions": {"allow": ["Bash(rg:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "Bash(stripe payment_intents:*)", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "Bash(# Create a comprehensive analysis file\ncat > /tmp/animation_analysis.txt << 'EOF'\nANIMATION & TRANSITION INCONSISTENCY ANALYSIS\n===========================================\n\nSTANDARD VARIABLES DEFINED:\n- --transition-base: all 0.2s ease-in-out\n- --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\n\nEOF\n\necho \"1. HARDCODED TRANSITION DURATIONS (not using CSS variables):\" >> /tmp/animation_analysis.txt\nrg -n \"transition:\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | grep -v \"var(--transition\" >> /tmp/animation_analysis.txt\n\necho -e \"\\n2. INCONSISTENT EASING FUNCTIONS:\" >> /tmp/animation_analysis.txt\nrg -n \"ease-in-out|ease-out|ease-in|cubic-bezier|ease[^-]\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' >> /tmp/animation_analysis.txt\n\necho -e \"\\n3. ANIMATION DURATION INCONSISTENCIES:\" >> /tmp/animation_analysis.txt\nrg -n \"animation.*[0-9]+\\.?[0-9]*s\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' >> /tmp/animation_analysis.txt\n\nhead -100 /tmp/animation_analysis.txt)", "Bash(# Continue the analysis\necho -e \"\\n4. MISSING TRANSITIONS ON INTERACTIVE ELEMENTS:\" >> /tmp/animation_analysis.txt\n\n# Find hover/focus states without transitions\nrg -l \":hover|:focus|:active\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | while read file; do\n    # Check if file has hover/focus but no transitions\n    if rg -q \":hover|:focus|:active\" \"$file\" && ! rg -q \"transition\" \"$file\"; then\n        echo \"MISSING TRANSITIONS: $file\" >> /tmp/animation_analysis.txt\n    fi\ndone\n\necho -e \"\\n5. TRANSFORM VALUES WITHOUT TRANSITIONS:\" >> /tmp/animation_analysis.txt\nrg -n \"transform:\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | head -20 >> /tmp/animation_analysis.txt\n\necho -e \"\\n6. KEYFRAMES ANALYSIS:\" >> /tmp/animation_analysis.txt\nrg -n \"@keyframes\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' >> /tmp/animation_analysis.txt\n\ntail -50 /tmp/animation_analysis.txt)", "Bash(# Let me create a summary focusing on the most critical inconsistencies\ncat > /tmp/critical_inconsistencies.md << 'EOF'\n# CRITICAL ANIMATION & TRANSITION INCONSISTENCIES FOUND\n\n## Overview\nThe codebase has standardized transition variables defined in `variables.css`:\n- `--transition-base: all 0.2s ease-in-out`\n- `--transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1)`\n\nHowever, there are **597 instances** of animation/transition patterns across 87 CSS files, with significant inconsistencies.\n\n## 1. HARDCODED TRANSITION DURATIONS (Should use CSS variables)\n\n### Most Common Violations:\n- **0.15s ease** - Used 24 times across admin components\n- **0.2s ease** - Used 20+ times (should use `--transition-base`)\n- **0.3s ease** - Used 15+ times (could use `--transition-smooth`)\n\n### Critical Files with Multiple Violations:\nEOF\n\n# Get files with multiple transition inconsistencies\nrg -l \"transition:\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | while read file; do\n    count=$(rg -c \"transition:\" \"$file\" 2>/dev/null || echo 0)\n    if [ \"$count\" -gt 3 ]; then\n        echo \"- \\`$file\\` - $count hardcoded transitions\" >> /tmp/critical_inconsistencies.md\n    fi\ndone\n\necho -e \"\\n## 2. EASING FUNCTION INCONSISTENCIES\\n\" >> /tmp/critical_inconsistencies.md\n\n# Count different easing functions\necho \"### Easing Functions Used:\" >> /tmp/critical_inconsistencies.md\nrg \"ease-in-out|ease-out|ease-in|cubic-bezier|ease[^-]\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | cut -d: -f3 | sed 's/.*\\(ease[^ ;]*\\|cubic-bezier[^;]*\\).*/\\1/' | sort | uniq -c | sort -nr >> /tmp/critical_inconsistencies.md\n\ncat /tmp/critical_inconsistencies.md)", "Bash(# Add animation duration inconsistencies\ncat >> /tmp/critical_inconsistencies.md << 'EOF'\n\n## 3. ANIMATION DURATION INCONSISTENCIES\n\n### Spin Animations (Different durations for same animation):\nEOF\n\nrg -n \"animation.*spin.*[0-9]+\\.?[0-9]*s\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | head -15 >> /tmp/critical_inconsistencies.md\n\ncat >> /tmp/critical_inconsistencies.md << 'EOF'\n\n### Other Animation Durations:\nEOF\n\nrg -n \"animation.*[0-9]+\\.?[0-9]*s\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | grep -v spin | head -10 >> /tmp/critical_inconsistencies.md\n\ncat >> /tmp/critical_inconsistencies.md << 'EOF'\n\n## 4. DUPLICATE KEYFRAMES\nMultiple spin animations defined instead of using a single global one:\nEOF\n\nrg -n \"@keyframes spin\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' >> /tmp/critical_inconsistencies.md\n\ncat >> /tmp/critical_inconsistencies.md << 'EOF'\n\n## 5. FOCUS STATE TRANSITION INCONSISTENCIES\n\n### Focus transitions found:\nEOF\n\nrg -n \"focus.*transition|transition.*focus\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' >> /tmp/critical_inconsistencies.md\n\n# Show the final report\ntail -100 /tmp/critical_inconsistencies.md)", "Bash(# Check for interactive elements without transitions\necho \"## 6. MISSING TRANSITIONS ON INTERACTIVE ELEMENTS\" >> /tmp/critical_inconsistencies.md\necho \"\" >> /tmp/critical_inconsistencies.md\n\n# Find files with hover states but no transitions in the same file\ntemp_files=$(mktemp)\nrg -l \":hover\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' > \"$temp_files\"\n\necho \"### Files with hover states but no transitions:\" >> /tmp/critical_inconsistencies.md\nwhile read -r file; do\n    if ! rg -q \"transition\" \"$file\" 2>/dev/null; then\n        echo \"- \\`$(basename \"$file\")\\` in \\`$(dirname \"$file\")\\`\" >> /tmp/critical_inconsistencies.md\n    fi\ndone < \"$temp_files\"\n\necho -e \"\\n### Transform values without transitions (potential animation issues):\" >> /tmp/critical_inconsistencies.md\nrg -n \"transform.*scale|transform.*translate|transform.*rotate\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | head -10 >> /tmp/critical_inconsistencies.md\n\necho -e \"\\n## 7. TRANSITION TIMING INCONSISTENCIES\" >> /tmp/critical_inconsistencies.md\necho \"\" >> /tmp/critical_inconsistencies.md\necho \"### Different transition durations for similar interactions:\" >> /tmp/critical_inconsistencies.md\necho \"- Button hover states: 0.15s, 0.2s, 0.3s (should be standardized)\" >> /tmp/critical_inconsistencies.md\necho \"- Card hover states: 0.2s, 0.3s (should be standardized)\" >> /tmp/critical_inconsistencies.md\necho \"- Modal fade transitions: 0.3s, 0.5s (should be standardized)\" >> /tmp/critical_inconsistencies.md\necho \"- Spinner animations: 0.6s, 1s, 1.2s, 1.5s (should be standardized)\" >> /tmp/critical_inconsistencies.md\n\nrm \"$temp_files\"\n\n# Display the final comprehensive report\ncat /tmp/critical_inconsistencies.md)", "Bash(# Create the final comprehensive report\ncat > /tmp/final_report.md << 'EOF'\n# COMPLETE ANIMATION & TRANSITION INCONSISTENCY REPORT\n\n## EXECUTIVE SUMMARY\nFound **597 animation/transition instances** across **87 CSS files** with severe inconsistencies:\n\n### Critical Issues:\n1. **200+ hardcoded transitions** instead of using CSS variables\n2. **25+ duplicate spin keyframes** (should be 1 global)\n3. **8 different easing functions** used inconsistently\n4. **6 different spin animation durations** (0.6s to 1.5s)\n5. **Missing transitions** on interactive elements\n\n---\n\n## DETAILED FINDINGS\n\n### 1. HARDCODED TRANSITIONS (Should use --transition-base or --transition-smooth)\n\n#### Worst Offenders (Files with 7+ violations):\nEOF\n\n# Get the worst files\nrg -l \"transition:\" /mnt/c/Users/<USER>/Desktop/Permisos_digitales/frontend/src --type css --type-add 'css:*.{css,module.css}' | while read file; do\n    count=$(rg -c \"transition:\" \"$file\" 2>/dev/null || echo 0)\n    if [ \"$count\" -ge 7 ]; then\n        echo \"**$file**: $count hardcoded transitions\" >> /tmp/final_report.md\n        # Show the actual violations\n        rg -n \"transition:\" \"$file\" | head -5 | sed 's/^/  - Line /' >> /tmp/final_report.md\n        echo \"\" >> /tmp/final_report.md\n    fi\ndone\n\necho \"### 2. SPIN ANIMATION CHAOS\" >> /tmp/final_report.md\necho \"\" >> /tmp/final_report.md\necho \"Found **25 duplicate spin keyframes** with different durations:\" >> /tmp/final_report.md\necho \"- 0.6s (button-styles.css:477)\" >> /tmp/final_report.md\necho \"- 1s (most common - 15 instances)\" >> /tmp/final_report.md  \necho \"- 1.2s (VerifyEmailPage.module.css:69)\" >> /tmp/final_report.md\necho \"- 1.5s (PermitsListPage.module.css:284)\" >> /tmp/final_report.md\necho \"\" >> /tmp/final_report.md\n\necho \"### 3. EASING FUNCTION CHAOS\" >> /tmp/final_report.md\necho \"\" >> /tmp/final_report.md\necho \"- **ease** - 174 instances (most common)\" >> /tmp/final_report.md\necho \"- **ease-in-out** - 17 instances (should be standard)\" >> /tmp/final_report.md\necho \"- **ease-out** - 7 instances\" >> /tmp/final_report.md\necho \"- **cubic-bezier(0.4, 0, 0.2, 1)** - 5 instances\" >> /tmp/final_report.md\necho \"- **ease-in** - 1 instance\" >> /tmp/final_report.md\necho \"\" >> /tmp/final_report.md\n\ncat /tmp/final_report.md)", "Bash(node:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(npm run typecheck:*)", "Bash(npm run type-check:*)", "Bash(npx tsc:*)", "Bash(npm test:*)", "Bash(npx vitest run:*)", "<PERSON><PERSON>(python3:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm --version)", "Bash(cp:*)", "<PERSON><PERSON>(true)", "Bash(./test-emergency-fixes.sh:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "Bash(/mcp)", "Bash(mcp status:*)", "Bash(stripe customers:*)", "Bash(stripe products:*)", "Bash(npm ls:*)", "Bash(pip install:*)", "Bash(npm run test:cov:*)", "Bash(npm search:*)", "Bash(npm audit:*)", "Bash(npm outdated:*)", "<PERSON><PERSON>(timeout:*)", "Bash(sudo service:*)", "Bash(pg_isready:*)", "Bash(psql:*)", "mcp__aws__get_my_ip", "mcp__aws__list_rds_instances", "mcp__aws__list_security_groups", "mcp__aws__add_security_group_rule", "mcp__postgres-prod__postgres_query", "mcp__postgres-dev__postgres_query", "Bash(git stash push:*)", "Bash(aws:*)", "Bash(git stash:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git merge:*)", "<PERSON><PERSON>(openssl:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(bash:*)", "Bash(tree:*)", "mcp__postgres-prod__postgres_list_tables", "<PERSON><PERSON>(wget:*)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/payment-monitoring.service.test.js --coverage)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/webhook-retry.service.test.js --coverage)", "Bash(NODE_ENV=test npm test -- src/repositories/__tests__/payment-recovery.repository.test.js --coverage)", "Bash(npm run test:security:*)", "Bash(npm run test-connection:*)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/payment-monitoring.service.test.js)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/payment-monitoring.service.test.js -t \"should determine correct severity levels\" 2>&1)", "Bash(npm start)", "Bash(DATABASE_PASSWORD_PROD=\"2bQXJ632zni3x8iRvvJc\" RDS_CERT_PATH=\"/mnt/c/Users/<USER>/Desktop/Permisos_digitales/certs/rds/rds-global-bundle.pem\" npm start)", "Bash(DATABASE_URL=\"postgresql://permisos_admin:<EMAIL>:5432/pd_db\" RDS_CERT_PATH=\"/mnt/c/Users/<USER>/Desktop/Permisos_digitales/certs/rds/rds-global-bundle.pem\" npm start 2 >& 1)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/payment-monitoring.service.test.js -t \"should determine correct severity levels\" 2 >& 1)", "Bash(NODE_ENV=test npm test -- src/services/__tests__/webhook-retry.service.test.js 2 >& 1)", "mcp__aws__get_aws_account_info", "Bash(NODE_ENV=test npm test -- src/services/__tests__/webhook-retry.service.test.js -t \"should process webhook successfully on retry\" 2 >& 1)", "mcp__postgres-dev__postgres_list_tables", "Bash(DATABASE_PASSWORD_PROD=\"2bQXJ632zni3x8iRvvJc\" node src/index-secure.js)", "Bash(NODE_ENV=production DATABASE_URL=\"postgresql://permisos_admin:<EMAIL>:5432/pd_db\" DATABASE_HOST=\"permisos-digitales-db-east.cgv8cw2gcp2x.us-east-1.rds.amazonaws.com\" DATABASE_PORT=\"5432\" DATABASE_NAME=\"pd_db\" DATABASE_USER=\"permisos_admin\" DATABASE_PASSWORD=\"2bQXJ632zni3x8iRvvJc\" ENFORCE_SSL=\"true\" MIN_TLS_VERSION=\"TLSv1.2\" RDS_CERT_PATH=\"/mnt/c/Users/<USER>/Desktop/Permisos_digitales/certs/rds/rds-global-bundle.pem\" MONITORING_ENABLED=\"true\" AUDIT_LOG_ENABLED=\"true\" timeout 5 node mcp-servers/postgres-prod-mcp-server/src/index-secure.js)", "Bash(npm run:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(npx playwright:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(echo:*)", "mcp__aws__list_ec2_instances", "mcp__stripe__stripe_list_products", "mcp__stripe__stripe_list_prices", "mcp__stripe__stripe_list_customers", "Bash(npm ci:*)", "Bash(npx vite:*)", "Bash(for f in src/admin/**/*.module.css)", "Bash(do echo -n \"$f: \")", "Bash(done)", "<PERSON><PERSON>(scp:*)", "Bash(ssh:*)", "mcp__aws__list_s3_objects", "Bash(tar:*)", "mcp__aws__list_s3_buckets", "mcp__aws__list_cloudformation_stacks", "mcp__aws__get_s3_object", "Bash(NODE_ENV=production npx vite build)", "Bash(redis-cli:*)", "Bash(telnet:*)", "mcp__postgres-dev__postgres_describe_table", "<PERSON><PERSON>(nslookup:*)", "mcp__postgres-dev__postgres_list_indexes", "Bash(pm2 restart:*)", "mcp__aws__list_lambda_functions", "Bash(./deploy-now.sh:*)", "Ba<PERSON>(dig:*)", "Bash(NODE_ENV=production node check-production-schema.js)", "<PERSON><PERSON>(claude config --help)", "<PERSON><PERSON>(claude config list:*)", "Bash(claude config get:*)", "Bash(claude config set:*)", "Bash(claude config add:*)", "Bash(ping:*)", "Bash(sudo rm:*)", "Bash(--exclude=\"node_modules\" )", "Bash(--exclude=\"frontend/node_modules\" )", "Bash(--exclude=\"mcp-servers/*/node_modules\" )", "Bash(--exclude=\".git\" )", "Bash(--exclude=\".gitignore\" )", "Bash(--exclude=\"*.log\" )", "Bash(--exclude=\"deployment_temp\" )", "Bash(--exclude=\"docs\" )", "Bash(--exclude=\".env.development\" )", "Bash(--exclude=\".env.local\" )", "Bash(--exclude=\"frontend/production-readiness-tasks\" )", "Bash(--exclude=\"uncommitted-files.txt\" )", "Bash(--exclude=\"nginx-*.conf\" )", "Bash(--exclude=\"ssl-setup.sh\")", "Bash(--exclude=node_modules )", "Bash(--exclude=frontend/node_modules )", "Bash(--exclude=.git )", "Bash(--exclude=.gitignore )", "Bash(--exclude=deployment_temp )", "Bash(--exclude=docs )", "Bash(--exclude=.env.development )", "Bash(--exclude=.env.local )", "Bash(--exclude=frontend/production-readiness-tasks )", "Bash(--exclude=uncommitted-files.txt )", "Bash(--exclude=ssl-setup.sh )", "<PERSON><PERSON>(.)", "Bash(./node_modules/.bin/tsc:*)", "Bash(NODE_ENV=development npx tsx logger-test.ts)", "Bash(NODE_ENV=production npx tsx logger-test.ts)", "<PERSON><PERSON>(diff:*)", "Bash(NODE_ENV=development node -e \"\nrequire('dotenv').config();\nconst config = require('./config');\nconsole.log('=== SESSION SECURITY CONFIGURATION TEST ===');\nconsole.log('Session Max Age (ms):', config.security.session.cookie.maxAge);\nconsole.log('Session Max Age (hours):', config.security.session.cookie.maxAge / 3600000);\nconsole.log('Session TTL (seconds):', config.security.session.store.ttl);\nconsole.log('Session TTL (hours):', config.security.session.store.ttl / 3600);\nconsole.log('Session Rotation Enabled:', config.security.session.rotation.enabled);\nconsole.log('Session Rotation Interval (ms):', config.security.session.rotation.interval);\nconsole.log('Session Rotation Interval (minutes):', config.security.session.rotation.interval / 60000);\nconsole.log('Session Max Regenerations:', config.security.session.rotation.maxRegenerations);\nconsole.log('Session Cookie Secure:', config.security.session.cookie.secure);\nconsole.log('Session Cookie HttpOnly:', config.security.session.cookie.httpOnly);\nconsole.log('Session Cookie SameSite:', config.security.session.cookie.sameSite);\nconsole.log('Session Rolling:', config.security.session.rolling);\nconsole.log('Session Store Prune Interval (seconds):', config.security.session.store.pruneSessionInterval);\nconsole.log('=== VERIFICATION PASSED ===');\n\")", "<PERSON><PERSON>(pkill:*)", "Bash(NODE_ENV=production node scripts/test-ssl-config.js)", "mcp__postgres-dev__postgres_list_databases", "Bash(--exclude='node_modules' )", "Bash(--exclude='.git' )", "Bash(--exclude='logs' )", "Bash(--exclude='storage' )", "Bash(--exclude='*.log' )", "Bash(--exclude='*.tar.gz' )", "Bash(--exclude='frontend' )", "Bash(--exclude='mcp-servers' )", "Bash(--exclude='.env*' )", "Bash(--exclude='deployment-package.tar.gz' )", "Bash(--exclude='docs' )", "Bash(--exclude='*.test.js' )", "Bash(--exclude='__tests__' )", "Bash(--exclude='coverage' )", "Bash(--exclude='.vscode' )", "Bash(--exclude='*.swp' )", "Bash(--exclude='*.swo' )", "Bash(--exclude='.DS_Store' )", "Bash(VITE_API_URL=http://**************:3001 npm run build:prod)", "Bash(stripe balance get:*)", "Bash(stripe balance retrieve:*)", "Bash(for file in docs/api/*.md)", "Bash(do echo \"=== $file ===\")", "Bash(# Create a script to update all files\ncat << 'EOF' > /tmp/update_logger_imports.sh\n#!/bin/bash\n\n# List of files to update\nfiles=$(find /mnt/c/Users/<USER>/Desktop/Permisos_digitales/src -name \"*.js\" -type f -exec grep -l \"require.*enhanced-logger\" {} \\;)\n\nfor file in $files; do\n    echo \"Updating: $file\"\n    \n    # Update the imports based on the relative path\n    sed -i \"s|require('\\./enhanced-logger')|require('./logger')|g\" \"$file\"\n    sed -i 's|require(\"./enhanced-logger\")|require(\"./logger\")|g' \"$file\"\n    \n    sed -i \"s|require('\\.\\.\\/utils\\/enhanced-logger')|require('../utils/logger')|g\" \"$file\"\n    sed -i 's|require(\"\\.\\.\\/utils\\/enhanced-logger\")|require(\"../utils/logger\")|g' \"$file\"\n    \n    sed -i \"s|require('\\.\\.\\/\\.\\.\\/utils\\/enhanced-logger')|require('../../utils/logger')|g\" \"$file\"\n    sed -i 's|require(\"\\.\\.\\/\\.\\.\\/utils\\/enhanced-logger\")|require(\"../../utils/logger\")|g' \"$file\"\n    \n    sed -i \"s|require('\\.\\.\\/\\.\\.\\/\\.\\.\\/utils\\/enhanced-logger')|require('../../../utils/logger')|g\" \"$file\"\n    sed -i 's|require(\"\\.\\.\\/\\.\\.\\/\\.\\.\\/utils\\/enhanced-logger\")|require(\"../../../utils/logger\")|g' \"$file\"\n    \n    sed -i \"s|require('\\.\\.\\/enhanced-logger')|require('../logger')|g\" \"$file\"\n    sed -i 's|require(\"\\.\\.\\/enhanced-logger\")|require(\"../logger\")|g' \"$file\"\n    \n    sed -i \"s|require('\\.\\.\\/\\.\\.\\/enhanced-logger')|require('../../logger')|g\" \"$file\"\n    sed -i 's|require(\"\\.\\.\\/\\.\\.\\/enhanced-logger\")|require(\"../../logger\")|g' \"$file\"\ndone\n\necho \"All files updated!\"\nEOF\n\nchmod +x /tmp/update_logger_imports.sh)", "Bash(PGPASSWORD=password psql -U permisos_user -d permisos_digitales_v2 -c \"SELECT name FROM pgmigrations ORDER BY run_on;\")", "Bash(PGPASSWORD=password psql -U permisos_user -d permisos_digitales_v2 -c \"SELECT pid, usename, application_name, state, query_start, state_change, wait_event_type, wait_event, query FROM pg_stat_activity WHERE datname = 'permisos_digitales_v2' AND state != 'idle' ORDER BY query_start;\")", "Bash(PGPASSWORD=password psql:*)", "Bash(kill:*)", "Bash(npx node-pg-migrate up:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["aws", "stripe-new", "stripe"]}