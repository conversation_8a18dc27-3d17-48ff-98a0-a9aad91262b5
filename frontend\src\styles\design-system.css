/* 
 * Permisos Digitales Design System
 * This file contains all design tokens and global styles
 * Import this in your main CSS file to ensure consistency
 */

:root {
  /* ============================================
     COLORS - Semantic color system
     ============================================ */
  
  /* Brand Colors */
  --color-brand: #B5384D;  /* Main red from logo */
  --color-brand-dark: #8A2B3A;
  --color-brand-light: #D4516A;
  --color-brand-lighter: #F5E8EA;
  
  /* Primary (using brand colors) */
  --color-primary: var(--color-brand);
  --color-primary-dark: var(--color-brand-dark);
  --color-primary-light: var(--color-brand-light);
  --color-primary-lighter: var(--color-brand-lighter);
  
  /* Neutral Colors */
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F5;
  --color-gray-200: #E5E5E5;
  --color-gray-300: #D4D4D4;
  --color-gray-400: #A3A3A3;
  --color-gray-500: #737373;
  --color-gray-600: #525252;
  --color-gray-700: #404040;
  --color-gray-800: #262626;
  --color-gray-900: #171717;
  
  /* Functional Colors */
  --color-success: #10B981;
  --color-success-light: #D1FAE5;
  --color-warning: #F59E0B;
  --color-warning-light: #FEF3C7;
  --color-error: #EF4444;
  --color-error-light: #FEE2E2;
  --color-info: #3B82F6;
  --color-info-light: #DBEAFE;
  
  /* Semantic Colors */
  --color-background: #FFFFFF;
  --color-background-alt: var(--color-gray-50);
  --color-surface: #FFFFFF;
  --color-surface-raised: #FFFFFF;
  --color-border: var(--color-gray-200);
  --color-border-strong: var(--color-gray-300);
  
  /* Text Colors */
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-muted: var(--color-gray-400);
  --color-text-inverse: #FFFFFF;
  
  /* ============================================
     TYPOGRAPHY
     ============================================ */
  
  /* Font Families */
  --font-sans: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-mono: "SF Mono", Monaco, "Inconsolata", "Fira Code", monospace;
  
  /* Font Sizes - Using rem for accessibility */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-base: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* ============================================
     SPACING - 8px base unit system
     ============================================ */
  
  --spacing-base: 8px;
  --spacing-xs: calc(var(--spacing-base) * 0.5);   /* 4px */
  --spacing-sm: calc(var(--spacing-base) * 1);     /* 8px */
  --spacing-md: calc(var(--spacing-base) * 2);     /* 16px */
  --spacing-lg: calc(var(--spacing-base) * 3);     /* 24px */
  --spacing-xl: calc(var(--spacing-base) * 4);     /* 32px */
  --spacing-2xl: calc(var(--spacing-base) * 6);    /* 48px */
  --spacing-3xl: calc(var(--spacing-base) * 8);    /* 64px */
  
  /* ============================================
     LAYOUT
     ============================================ */
  
  /* Container Widths */
  --container-xs: 475px;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  
  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* ============================================
     EFFECTS
     ============================================ */
  
  /* Shadows - Consistent elevation system */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-base: all 150ms ease-in-out;
  --transition-fast: all 100ms ease-in-out;
  --transition-slow: all 300ms ease-in-out;
  
  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* ============================================
   GLOBAL STYLES - Base resets and defaults
   ============================================ */

/* Modern CSS Reset */
*, *::before, *::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html, body {
  height: 100%;
}

body {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}

input, button, textarea, select {
  font: inherit;
}

p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}

/* ============================================
   COMPONENT STYLES - Standardized components
   ============================================ */

/* Buttons - Consistent button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-base);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-primary-lighter);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
  border-color: var(--color-error);
}

.btn-danger:hover:not(:disabled) {
  background-color: #DC2626;
  border-color: #DC2626;
}

/* Button Sizes */
.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

/* Form Controls - Consistent input styles */
.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: var(--transition-base);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-lighter);
}

.form-control::placeholder {
  color: var(--color-text-muted);
}

/* Cards - Consistent card styles */
.card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.card-raised {
  box-shadow: var(--shadow-md);
}

/* Utility Classes */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-error { color: var(--color-error); }
.text-success { color: var(--color-success); }

.bg-primary { background-color: var(--color-primary); }
.bg-light { background-color: var(--color-background-alt); }
.bg-white { background-color: var(--color-surface); }

/* Spacing utilities */
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }

/* Responsive utilities */
@media (max-width: 768px) {
  :root {
    /* Adjust font sizes for mobile */
    --font-size-base: 0.9375rem; /* 15px */
    --font-size-lg: 1.0625rem;   /* 17px */
    --font-size-xl: 1.1875rem;   /* 19px */
    --font-size-2xl: 1.375rem;   /* 22px */
    --font-size-3xl: 1.625rem;   /* 26px */
  }
}