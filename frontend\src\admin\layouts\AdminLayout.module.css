/* Admin Layout Styles */
.adminLayout {
  display: flex;
  min-height: 100vh;
  position: relative;
}

/* Overlay for mobile sidebar */
.overlay {
  position: fixed;
  inset: 0;
  background-color: rgb(0 0 0 / 50%);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  cursor: pointer;
}

.overlayVisible {
  opacity: 1;
  pointer-events: auto;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: var(--color-neutral-900, #343a40);
  color: var(--color-white, #fff);
  display: flex;
  flex-direction: column;
  transition:
    transform 0.3s ease,
    width 0.3s ease;
  position: fixed;
  height: 100vh;
  z-index: 1000;
  transform: translateX(0);
}

/* Mobile sidebar styles */
.sidebar.mobile {
  width: 280px; /* Fixed width for consistency */
  max-width: 85vw; /* But not more than 85% of viewport */
  left: auto; /* Reset left position */
  right: 0; /* Position from right side */
  transform: translateX(100%); /* Start off-screen to the right */
  box-shadow: none;
}

.sidebar.mobile.open {
  transform: translateX(0); /* Slide in from right */
  box-shadow: -2px 0 12px rgb(0 0 0 / 10%);
}

/* Desktop sidebar collapsed state */
.sidebar:not(.mobile, .open) {
  width: 60px;
}

.sidebarHeader {
  padding: 0 var(--space-3);
  height: var(--mobile-header-height, 56px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  box-sizing: border-box;
  flex-shrink: 0;
}

.sidebarLogo {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.textLogo {
  color: var(--color-white) !important;
  font-size: 1.25rem;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
}

/* Ensure the logo is centered in collapsed state */
.sidebar:not(.mobile, .open) .sidebarLogo {
  justify-content: center;
  padding-left: 0;
}

.textLogo span {
  color: var(--color-primary-light) !important;
}

.textLogo:hover {
  transform: translateY(-1px);
}

.sidebarCloseButton {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
}

.sidebarUser {
  padding: 1rem;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
}

/* Adjust padding and remove border in collapsed sidebar user section */
.sidebar:not(.mobile, .open) .sidebarUser {
  padding: 0;
  min-height: 0;
  border-bottom: none;
}

.userInfo {
  display: flex;
  flex-direction: column;
}

.userName {
  font-weight: 600;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userEmail {
  font-size: 0.75rem;
  color: rgb(255 255 255 / 70%);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebarNav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.navLink {
  display: flex;
  align-items: center;
  padding: 0.85rem 1.25rem; /* Increased padding for better touch targets */
  color: rgb(255 255 255 / 70%);
  text-decoration: none;
  transition: all 0.15s ease;
  min-height: 44px; /* Minimum height for touch targets */
  margin-bottom: 4px; /* Add spacing between links */
}

.navLink:hover {
  background-color: rgb(255 255 255 / 10%);
  color: #fff;
}

.navLink.active {
  background-color: #a72b31;
  color: #fff;
}

.navIcon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Collapsed sidebar styles */
.sidebar:not(.mobile, .open) .navText {
  display: none;
}

/* Center icons in collapsed state */
.sidebar:not(.mobile, .open) .navIcon {
  margin-right: 0;
  margin-left: 0;
  justify-content: center;
  width: 100%;
}

/* Ensure logo container doesn't overflow in collapsed state */
.sidebar:not(.mobile, .open) .sidebarLogo {
  overflow: hidden;
}

/* Hide user info in collapsed sidebar (desktop) */
.sidebar:not(.mobile, .open) .userInfo {
  display: none;
}

/* Additional styling for navLinks in collapsed state */
.sidebar:not(.mobile, .open) .navLink {
  justify-content: center;
  padding: 0.85rem 0;
}

.sidebarFooter {
  padding: 1rem;
  border-top: 1px solid rgb(255 255 255 / 10%);
  margin-top: auto; /* Push to bottom of sidebar */
}

/* Adjust padding in collapsed sidebar footer */
.sidebar:not(.mobile, .open) .sidebarFooter {
  padding: 1rem 0.5rem; /* Reduce horizontal padding in collapsed state */
}

.footerActions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.logoutButton {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  background-color: rgb(255 255 255 / 10%);
  border: none;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.logoutButton:hover {
  background-color: rgb(255 255 255 / 20%);
}

/* These styles are for the default state of the logoutIcon.
   The Icon component itself and the Button component's internal structure
   will handle the actual icon sizing and centering.
   We only apply color or specific margins if needed.
*/
.logoutIcon {
  /* Removed font-size, margin-right, min-width, display, align-items, justify-content */

  /* These should be handled by the Icon component and Button component's internal flexbox */
  color: var(--color-white); /* Keep explicit color if needed */
}

.sidebarToggleButton {
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(255 255 255 / 5%);
  border: none;
  color: rgb(255 255 255 / 60%);
  cursor: pointer;
  transition: all 0.15s ease;
  border-radius: 50%;
  margin: 0 auto var(--space-2);
}

.sidebarToggleButton:hover {
  background-color: rgb(255 255 255 / 10%);
  color: var(--color-white);
}

.sidebar:not(.mobile, .open) .sidebarToggleButton {
  margin-bottom: var(--space-3);
}

/* Control icon size within the toggle button - relies on Button.tsx passing size to Icon component */
.sidebarToggleButton .icon {
  /* Removed font-size directly here, rely on Icon component's size prop */
  display: flex; /* Ensure flexbox for centering */
  align-items: center;
  justify-content: center;
}

.sidebar:not(.mobile, .open) .logoutText {
  display: none;
}

/* Styles for the logout button when sidebar is collapsed (desktop) */
.sidebar:not(.mobile, .open) .logoutButton {
  /* These rules are for the button container itself, not its internal icon */
  display: flex; /* Use flexbox to center its content (the icon) */
  align-items: center; /* Center content vertically */
  justify-content: center; /* Center content horizontally */
  width: 44px; /* Fixed width for icon-only button */
  height: 44px; /* Fixed height for icon-only button */
  min-width: 44px; /* Ensure consistent sizing */
  min-height: 44px; /* Ensure consistent sizing */
  max-width: 44px; /* Prevent expansion */
  max-height: 44px; /* Prevent expansion */
  padding: 0; /* No internal padding, let the icon fill it */
  background-color: rgb(255 255 255 / 10%);
  border: none;
  border-radius: 4px;
  margin: 0 auto; /* Center the button within the footer */
  overflow: hidden; /* Hide any overflow */
}

/* Remove any conflicting overrides for the button's internal icon wrapper */
.sidebar:not(.mobile, .open) .logoutButton :global(.buttonIcon) {
  /* Removed `width`, `height`, `font-size` overrides */

  /* These are now handled by the Icon component and Button's internal styling */
  margin: 0; /* Ensure no margin from the `Button` component's internal icon wrapper */
}

/* Main Content Styles */
.mainContent {
  flex: 1;
  margin-left: 250px;
  padding: var(--space-fluid-3, 2rem);
  background-color: var(--color-neutral-100, #f8f9fa);
  transition: margin-left 0.3s ease;
}

.mainContent.expanded {
  margin-left: 60px;
}

/* Mobile content styles */
.mobileContent {
  margin-left: 0;
  padding-top: calc(60px + var(--space-fluid-3, 2rem)); /* Account for mobile header */
}

/* Responsive Styles */
@media (width <= 768px) {
  /* Mobile sidebar text visibility */
  .sidebar.mobile:not(.open) .navText,
  .sidebar.mobile:not(.open) .logoutText {
    display: none;
  }

  .sidebar.mobile.open .navText,
  .sidebar.mobile.open .logoutText {
    display: inline;
  }

  /* Mobile content always takes full width */
  .mainContent.mobileContent {
    margin-left: 0;
  }

  /* Ensure logo is visible on mobile */
  .sidebar.mobile .textLogo {
    font-size: 1.2rem;
  }

  /* Position the logo on mobile */
  .sidebar.mobile .sidebarLogo {
    justify-content: flex-start;
    padding-left: var(--space-2);
  }

  /* Adjust footer spacing on mobile */
  .sidebar.mobile .footerActions {
    gap: 1rem;
  }

  /* Ensure the sidebar close button is properly positioned */
  .sidebar.mobile .sidebarCloseButton {
    position: absolute;
    right: var(--space-2);
    top: 50%;
    transform: translateY(-50%);
  }

  /* Ensure the sidebar header has proper positioning context */
  .sidebar.mobile .sidebarHeader {
    position: relative;
  }
}

/* Tablet Styles */
@media (width <= 576px) {
  .mainContent {
    padding: var(--space-fluid-2, 1.25rem);
  }

  .navLink {
    padding: 0.8rem 1.25rem;
  }
}

/* Mobile Styles */
@media (width <= 480px) {
  .mainContent {
    padding: 1rem;
  }

  .textLogo {
    font-size: 1.1rem;
  }
}

/* Very Small Mobile Styles */
@media (width <= 360px) {
  .mainContent {
    padding: 0.75rem;
  }

  .navLink {
    padding: 0.75rem 1rem;
  }

  /* Ensure touch targets are large enough on small screens */
  .sidebarToggleButton {
    min-width: 44px;
    min-height: 44px;
  }
}
