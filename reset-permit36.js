// Reset permit 36 to trigger regeneration
const { Pool } = require('pg');

async function resetPermit36() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  console.log('🔄 RESETTING PERMIT 36 FOR REGENERATION');
  
  const result = await pool.query(`
    UPDATE permit_applications 
    SET 
      status = 'PAYMENT_RECEIVED',
      permit_file_path = NULL,
      certificado_file_path = NULL,
      placas_file_path = NULL,
      queue_status = NULL,
      queue_job_id = NULL,
      updated_at = NOW()
    WHERE id = 36
    RETURNING id, status, folio, user_id
  `);
  
  if (result.rows.length > 0) {
    const permit = result.rows[0];
    console.log(`✅ Reset permit ${permit.id} (${permit.folio}) for user ${permit.user_id}`);
    console.log(`   New status: ${permit.status}`);
    console.log(`   This will trigger PDF regeneration with proper S3 upload`);
  } else {
    console.log('❌ Permit 36 not found or not updated');
  }
  
  await pool.end();
  process.exit(0);
}

resetPermit36().catch(console.error);
