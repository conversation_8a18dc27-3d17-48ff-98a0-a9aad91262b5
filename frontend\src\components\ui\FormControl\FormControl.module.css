/* Soft & Trustworthy Checkbox and Radio Styles */

/* Hide Default Input */
.checkboxInput,
.radioInput {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 22px;
  height: 22px;
  margin: 0;
  z-index: 1;
}

/* Error Message */
.errorMessage {
  color: var(--color-danger, #dc3545);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Custom Checkbox Styles */
.checkboxWrapper {
  display: flex;
  position: relative;
  padding-left: 36px; /* Increased for better touch target */
  margin-bottom: 12px;
  cursor: pointer;
  user-select: none;
  min-height: 44px; /* Minimum height for WCAG-compliant touch target */
  align-items: center; /* Vertically center the content */
  padding-top: 4px; /* Add some vertical padding */
  padding-bottom: 4px; /* Add some vertical padding */
  isolation: isolate; /* Create new stacking context to contain absolute elements */
  box-sizing: border-box;
}

.checkboxWrapper .checkboxInput ~ .checkboxIndicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%); /* Center vertically */
  left: 0;
  height: 22px; /* Increased size for better touch target */
  width: 22px; /* Increased size for better touch target */
  background-color: var(--bs-white);
  border: 1px solid var(--bs-gray-400);
  border-radius: 4px; /* Slightly rounded */
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
  box-sizing: border-box;
  pointer-events: none; /* Ensure clicks pass through to the input */
}

.checkboxWrapper:hover .checkboxInput ~ .checkboxIndicator {
  border-color: var(--bs-gray-600);
}

.checkboxWrapper .checkboxInput:checked ~ .checkboxIndicator {
  background-color: var(--rojo);
  border-color: var(--rojo);
  box-shadow: 0 1px 3px rgb(167 43 49 / 20%);
}

.checkboxWrapper .checkboxInput:disabled ~ .checkboxIndicator {
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
  cursor: not-allowed;
  box-shadow: none;
}

.checkboxWrapper .checkboxIndicator::after {
  content: '';
  position: absolute;
  display: none;
  left: 8px; /* Adjusted for larger indicator */
  top: 4px; /* Adjusted for larger indicator */
  width: 6px; /* Slightly larger checkmark */
  height: 12px; /* Slightly larger checkmark */
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkboxWrapper .checkboxInput:checked ~ .checkboxIndicator::after {
  display: block;
}

.checkboxWrapper .checkboxInput:disabled ~ * {
  color: var(--bs-gray-500);
  cursor: not-allowed;
}

/* Custom Radio Styles */
.radioWrapper {
  display: flex;
  position: relative;
  padding-left: 36px; /* Increased for better touch target */
  margin-bottom: 12px;
  cursor: pointer;
  user-select: none;
  min-height: 44px; /* Minimum height for WCAG-compliant touch target */
  align-items: center; /* Vertically center the content */
  padding-top: 4px; /* Add some vertical padding */
  padding-bottom: 4px; /* Add some vertical padding */
}

.radioWrapper .radioInput ~ .radioIndicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%); /* Center vertically */
  left: 0;
  height: 22px; /* Increased size for better touch target */
  width: 22px; /* Increased size for better touch target */
  background-color: var(--bs-white);
  border: 1px solid var(--bs-gray-400);
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
}

.radioWrapper:hover .radioInput ~ .radioIndicator {
  border-color: var(--bs-gray-600);
}

.radioWrapper .radioInput:checked ~ .radioIndicator {
  border: 5px solid var(--rojo); /* Filled circle effect */
  box-shadow: 0 1px 3px rgb(167 43 49 / 20%);
}

.radioWrapper .radioInput:disabled ~ .radioIndicator {
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
  cursor: not-allowed;
  box-shadow: none;
}

.radioWrapper .radioInput:disabled:checked ~ .radioIndicator {
  background-color: var(--bs-gray-200);
  border: 5px solid var(--bs-gray-400);
}

.radioWrapper .radioInput:disabled ~ * {
  color: var(--bs-gray-500);
  cursor: not-allowed;
}

/* Select Styles */
.select {
  display: block;
  width: 100%;
  padding: 12px 16px; /* Increased padding for better touch targets */
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--bs-gray-900);
  background-color: var(--bs-white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center; /* Adjusted for better touch area */
  background-size: 16px 12px;
  border: 1px solid var(--bs-gray-300);
  border-radius: 8px;
  box-shadow: inset 0 1px 2px rgb(0 0 0 / 7%);
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
  appearance: none;
  min-height: 44px; /* Minimum height for touch targets */
  box-sizing: border-box; /* Ensure padding is included in height */
}

.select:focus {
  border-color: var(--rojo);
  outline: 0;
  box-shadow:
    0 0 0 3px rgb(167 43 49 / 15%),
    inset 0 1px 2px rgb(0 0 0 / 7%);
}

.select:disabled {
  background-color: var(--bs-gray-100);
  color: var(--bs-gray-500);
  cursor: not-allowed;
  border-color: var(--bs-gray-200);
  box-shadow: none;
}
