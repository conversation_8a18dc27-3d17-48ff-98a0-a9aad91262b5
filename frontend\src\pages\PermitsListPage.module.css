/* PermitsListPage.module.css */

/* Page Container */
.permitsListContainer {
  min-height: calc(100vh - 64px); /* Assuming 64px header height */
  background-color: var(--color-neutral-100);
  padding-bottom: var(--space-8);
}

/* Breadcrumbs */
.breadcrumbs {
  margin-bottom: var(--space-4);

  /* Default display handled by ClientBreadcrumbs component or global styles */
}

/* Page Header */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-5);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--color-neutral-200);
  flex-wrap: wrap; /* Allow wrapping on small screens */
  gap: var(--space-3); /* Gap for wrapped items */
}

.pageTitle {
  font-family: var(--font-family-headings);
  font-size: clamp(1.75rem, 4vw, 2.25rem); /* Responsive font size */
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
  margin: 0;
  line-height: 1.2;
}

.newPermitButton {
  min-width: 150px; /* Reduced for mobile */
  width: 100%;
  max-width: 220px;

  /* Button component handles its own height and touch target size */
}

/* Filter Controls */
.filterContainer {
  margin-bottom: var(--space-5);
  position: relative;
}

.filterButtonsWrapper {
  display: flex;
  gap: var(--space-2);
  overflow-x: auto;
  padding-bottom: var(--space-2); /* For scrollbar visibility if not hidden */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.filterButtonsWrapper::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.filterButton {
  background-color: var(--color-white);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--border-radius-pill); /* Pill shape */
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-700);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-height: 40px;
  display: inline-flex; /* Changed to inline-flex */
  align-items: center;
  justify-content: center;
  gap: var(--space-2); /* Gap between icon and text */
  box-shadow: var(--shadow-xs);
}

.filterButton:hover {
  background-color: var(--color-neutral-50);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

.filterButton.active {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

.filterButton.active:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.filterButtonIcon {
  font-size: var(--text-md);
  line-height: 1; /* Ensure icon aligns well */
}

/* Permits Grid */
.permitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
  margin-top: var(--space-2); /* Reduced from space-4 since filter has margin-bottom */
}

/* Permit Card - New Design */
.permitCard {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition:
    transform 0.2s ease-out,
    box-shadow 0.2s ease-out;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-neutral-200);
}

.permitCard:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to the top */
  padding: var(--space-3) var(--space-4) var(--space-2);
  border-bottom: 1px solid var(--color-neutral-100); /* Softer border */
}

.vehicleInfo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.vehicleIcon {
  color: var(--color-primary);
  font-size: 1.8rem; /* Larger icon */
  flex-shrink: 0;
}

.vehicleDetails {
  display: flex;
  flex-direction: column;
}

.vehicleMakeModel {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
  line-height: 1.3;
}

.vehicleYear {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--border-radius-pill);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  white-space: nowrap;
  margin-left: var(--space-2); /* Space from vehicle info if they wrap */
}

.statusBadge > svg {
  font-size: 0.8em; /* Slightly smaller than text */
}

/* Status Badge Colors */
.statusActive {
  background-color: var(--status-success-bg);
  color: var(--status-success);
}

.statusExpiringSoon {
  background-color: var(--status-warning-bg);
  color: var(--status-warning-hover); /* Darker yellow for text */
}

.statusNeedsAttention {
  background-color: var(--status-critical-bg);
  color: var(--status-critical);
}

.statusArchived {
  background-color: var(--status-neutral-bg);
  color: var(--status-neutral);
}

.cardBody {
  padding: var(--space-3) var(--space-4);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.detailItem {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-neutral-700);
}

.detailIcon {
  color: var(--color-neutral-500);
  font-size: 1.1em; /* Slightly larger than text */
  flex-shrink: 0;
}

.detailLabel {
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-600);
}

.detailValue {
  font-weight: var(--font-weight-normal);
  color: var(--color-neutral-800);
}

.cardFooter {
  padding: var(--space-3) var(--space-4);
  border-top: 1px solid var(--color-neutral-200);
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap; /* Allow buttons to wrap on small card widths */
}

.ctaButton {
  flex: 1 1 auto; /* Allow buttons to grow and shrink, auto basis */
  min-width: 130px; /* Minimum width for each button */
}

.ctaIcon {
  font-size: var(--text-md); /* Ensure consistency with button component */
}

/* Loading, Error, and Empty States */
.stateContainer {
  grid-column: 1 / -1; /* Span full width of the grid */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  min-height: 300px;
  border: 1px solid var(--color-neutral-200);
}

.stateIcon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  color: var(--color-neutral-500);
}

.loadingIcon {
  color: var(--color-primary);
  animation: spin var(--animation-duration-slower) linear infinite;
}

.errorIcon {
  color: var(--status-critical);
}

.emptyIcon {
  color: var(--color-neutral-400);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.stateText {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-800);
  margin-bottom: var(--space-2);
}

.stateSubText {
  font-size: var(--text-md);
  color: var(--color-neutral-600);
  max-width: 450px;
  line-height: 1.6;
}

/* Responsive adjustments */
@media (width <= 768px) {
  /* Tablet and below */
  .permitsGrid {
    grid-template-columns: 1fr; /* Single column on smaller tablets */
    gap: var(--space-4);
  }

  .pageHeader {
    flex-direction: column;
    align-items: stretch; /* Make title and button full width */
    gap: var(--space-3);
  }

  .pageTitle {
    text-align: center;
  }

  .newPermitButton {
    width: 100%; /* Full width button on mobile */
  }

  .filterContainer {
    margin-left: calc(-1 * var(--space-3)); /* Bleed to screen edges */
    margin-right: calc(-1 * var(--space-3));
    padding-left: var(--space-3);
    padding-right: var(--space-3);
    width: calc(100% + var(--space-6));
}

@media (width <= 480px) {
  /* Mobile */
  .pageTitle {
    font-size: 1.5rem; /* Smaller title on mobile */
  }

  .filterButton {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .permitCard {
    border-radius: var(--border-radius-md);
  }

  .cardHeader {
    padding: var(--space-3);
    flex-direction: column; /* Stack vehicle info and badge */
    align-items: stretch; /* Make badge full width if needed */
    gap: var(--space-2);
  }

  .statusBadge {
    align-self: flex-start; /* Align badge to start when stacked */
    margin-left: 0;
  }

  .vehicleIcon {
    font-size: 1.5rem;
  }

  .vehicleMakeModel {
    font-size: var(--text-md);
  }

  .cardBody {
    padding: var(--space-3);
  }

  .cardFooter {
    padding: var(--space-3);
    flex-direction: column; /* Stack buttons */
  }

  .ctaButton {
    width: 100%;
  }

  .stateText {
    font-size: var(--text-md);
  }

  .stateSubText {
    font-size: var(--text-sm);
  }

  .stateIcon {
    font-size: 2.5rem;
}

/* Extra small screens (360px) */
@media (max-width: 360px) {
  .permitsListContainer {
    padding: var(--space-3);
  }
  
  .pageHeader {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pageTitle {
    font-size: 1.25rem;
    margin-bottom: var(--space-3);
  }
  
  .newPermitButton {
    min-width: 100%;
  }
  
  .permitsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .filterButton {
    min-height: 44px; /* Touch target */
    padding: var(--space-2);
  }
  
  .vehicleInfo {
    gap: var(--space-2);
  }
  
  .vehicleIcon {
    font-size: 1.25rem;
  }
  
  .vehicleMakeModel {
    font-size: var(--text-sm);
  }
  
  .permitCard {
    margin: 0 calc(-1 * var(--space-3)); /* Full width on small screens */
    border-radius: 0;
    border-left: none;
    border-right: none;
}
}
}
}
