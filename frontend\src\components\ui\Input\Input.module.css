/* Soft & Trustworthy Input Styles */
.input {
  padding: 12px 16px; /* Increased padding for better touch targets */
  border: 1px solid var(--bs-gray-300);
  border-radius: 8px; /* Rounded */
  background-color: var(--bs-white);
  color: var(--bs-gray-900);
  width: 100%;
  box-shadow: inset 0 1px 2px rgb(0 0 0 / 7%); /* Subtle inner shadow */
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
  min-height: 44px; /* Minimum height for touch targets */
  box-sizing: border-box; /* Ensure padding is included in height */
}

.input::placeholder {
  color: var(--bs-gray-500);
}

.input:focus {
  /* Softer focus */
  outline: none;
  border-color: var(--rojo);
  box-shadow:
    0 0 0 3px rgb(167 43 49 / 15%),
    inset 0 1px 2px rgb(0 0 0 / 7%); /* Softer outer glow */
}

.inputError {
  /* Error style */
  border-color: var(--rojo);
  background-color: rgb(167 43 49 / 2%); /* Faint red background */
}

.input:disabled {
  background-color: var(--bs-gray-100);
  color: var(--bs-gray-500);
  cursor: not-allowed;
  box-shadow: none;
  border-color: var(--bs-gray-200);
}
