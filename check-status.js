const db = require('./src/db');

async function checkStatus() {
  try {
    console.log('Checking permit status after triggering PDF regeneration...\n');
    
    // Check status of permits 14 and 36
    const query = `
      SELECT id, status, queue_status, queue_job_id, permit_file_path, updated_at
      FROM permit_applications 
      WHERE id IN (14, 36)
      ORDER BY id
    `;
    
    const result = await db.query(query);
    
    result.rows.forEach(row => {
      console.log(`Permit ID: ${row.id}`);
      console.log(`  Status: ${row.status}`);
      console.log(`  Queue Status: ${row.queue_status || 'NULL'}`);
      console.log(`  Queue Job ID: ${row.queue_job_id || 'NULL'}`);
      console.log(`  File Path: ${row.permit_file_path || 'NULL'}`);
      console.log(`  Updated: ${row.updated_at}`);
      console.log('');
    });
    
    // Check if there are any pending PDF generations
    const pendingQuery = `
      SELECT COUNT(*) as count
      FROM permit_applications 
      WHERE status = 'PAYMENT_RECEIVED'
    `;
    
    const pendingResult = await db.query(pendingQuery);
    console.log(`Permits pending PDF generation: ${pendingResult.rows[0].count}`);
    
  } catch (error) {
    console.error('Error checking status:', error.message);
  } finally {
    process.exit(0);
  }
}

checkStatus();
