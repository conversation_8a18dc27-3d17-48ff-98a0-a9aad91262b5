/* Users Page Styles */
/* Container styling now handled by ResponsiveContainer component */

/* Page Header */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Search Container */
.searchContainer {
  display: flex;
  gap: 0.5rem;
}

.searchInputWrapper {
  position: relative;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.875rem;
}

.searchInput {
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid #ced4da;
  font-size: 0.875rem;
  min-width: 250px;
}

.searchInput:focus {
  outline: none;
  border-color: #a72b31;
}

.filterWrapper {
  position: relative;
}

.filterIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.875rem;
}

.filterSelect {
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid #ced4da;
  font-size: 0.875rem;
  min-width: 180px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M.5 1.5l3 3 3-3H.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 8px 8px;
}

.filterSelect:focus {
  outline: none;
  border-color: #a72b31;
}

.refreshButton {
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  color: #343a40;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.15s ease;
}

.refreshButton:hover {
  background-color: #e9ecef;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem;
}

.pageSubtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Table Styles */
.tableContainer {
  overflow-x: auto;
}

.usersTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.usersTable th,
.usersTable td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.usersTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #343a40;
}

.usersTable tr:hover {
  background-color: #f8f9fa;
}

.clickableRow {
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.clickableRow:hover {
  background-color: #e9ecef;
}

/* Admin Badge */
.adminBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0;
  background-color: #cce5ff;
  color: #004085;
}

.clientBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0;
  background-color: #d4edda;
  color: #155724;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem;
  background-color: white;
  border: 1px solid #e9ecef;
}

.emptyState h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem;
}

.emptyState p {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgb(0 0 0 / 10%);
  border-radius: 50%;
  border-top-color: #a72b31;
  animation: spin var(--animation-duration-base) ease-in-out infinite;
  margin-bottom: 1rem;
}

.errorIcon {
  font-size: 3rem;
  color: #a72b31;
  margin-bottom: 1rem;
}

.retryButton {
  padding: 0.5rem 1rem;
  background-color: #a72b31;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.15s ease;
}

.retryButton:hover {
  background-color: #852d2d;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.paginationButton {
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid #ced4da;
  color: #343a40;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.15s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #e9ecef;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationInfo {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Mobile Cards */
.mobileCards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileCard {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
}

.mobileCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
  border-color: #a72b31;
}

.mobileCard:active {
  transform: translateY(0);
}

.mobileCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.mobileCardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #343a40;
  line-height: 1.3;
}

.mobileCardId {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.mobileCardContent {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobileCardItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
}

.mobileCardLabel {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 1rem;
}

.mobileCardValue {
  font-size: 0.875rem;
  color: #343a40;
  text-align: right;
  word-break: break-word;
}

/* Responsive Styles */
@media (width <= 768px) {
  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .pageTitle {
    font-size: 1.5rem;
  }

  .searchContainer {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .searchInputWrapper {
    width: 100%;
  }

  .searchInput {
    width: 100%;
    min-width: auto;
  }

  .filterWrapper {
    width: 100%;
  }

  .filterSelect {
    width: 100%;
    min-width: auto;
  }

  .tableContainer {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .usersTable {
    min-width: 600px;
}

@media (width <= 480px) {
  .pageTitle {
    font-size: 1.25rem;
  }

  .pageSubtitle {
    font-size: 0.875rem;
  }

  .searchInput,
  .filterSelect {
    font-size: 0.875rem;
    height: 44px;
  }

  /* Mobile card improvements */
  .mobileCard {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  .mobileCardTitle {
    font-size: 1rem;
  }

  .mobileCardLabel,
  .mobileCardValue {
    font-size: 0.8rem;
  }

  .mobileCardId {
    font-size: 0.8rem;
  }

  .usersTable {
    font-size: 0.8rem;
  }

  .usersTable th,
  .usersTable td {
    padding: 0.5rem;
  }

  .adminBadge,
  .clientBadge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }

  .paginationButton {
    width: 100%;
    max-width: 150px;
  }

  .paginationInfo {
    font-size: 0.8rem;
}

@media (width <= 360px) {
  .usersTable {
    min-width: 500px;
  }

  .emptyState {
    padding: 2rem 1rem;
  }

  .emptyState h2 {
    font-size: 1.1rem;
  }

  .emptyState p {
    font-size: 0.9rem;
}
}
}
}
