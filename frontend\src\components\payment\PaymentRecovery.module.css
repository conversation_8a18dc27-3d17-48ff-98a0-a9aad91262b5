/* PaymentRecovery.module.css */

.recoveryContainer {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: var(--space-6);
  margin: var(--space-4) 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recoveryHeader {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-5);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--color-border);
}

.recoveryHeader h3 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.warningIcon {
  color: var(--color-warning);
}

.statusSection {
  margin-bottom: var(--space-5);
}

.statusMessage {
  font-size: 1rem;
  color: var(--color-text-primary);
  margin-bottom: var(--space-3);
  padding: var(--space-3);
  background: var(--color-background-secondary);
  border-radius: 8px;
  border-left: 4px solid var(--color-primary);
}

.lastAttempt {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.attemptsInfo {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.actionButtons {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-5);
  flex-wrap: wrap;
}

@media (max-width: 480px) {
  .actionButtons {
    flex-direction: column;
  }
  
  .actionButtons button {
    width: 100%;
  }
}

.checkButton,
.recoveryButton,
.retryButton {
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.spinningIcon {
  animation: spin var(--animation-duration-base) linear infinite;
}


  to {
    transform: rotate(360deg);
  }
}

.helpText {
  background: var(--color-background-secondary);
  padding: var(--space-4);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.helpText h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--color-text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.helpText ul {
  margin: 0 0 var(--space-4) 0;
  padding-left: var(--space-4);
}

.helpText li {
  margin-bottom: var(--space-2);
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.helpText li strong {
  color: var(--color-text-primary);
}

.contactInfo {
  margin: 0;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.contactInfo a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
}

.contactInfo a:hover {
  text-decoration: underline;
}

/* Loading states */
.recoveryContainer[data-loading="true"] {
  opacity: 0.7;
  pointer-events: none;
}

/* Success state */
.recoveryContainer[data-status="success"] {
  border-color: var(--color-success);
  background: var(--color-success-light, #f0f9f0);
}

.recoveryContainer[data-status="success"] .statusMessage {
  border-left-color: var(--color-success);
  background: var(--color-success-light, #f0f9f0);
  color: var(--color-success-dark, #2d5a2d);
}

/* Error state */
.recoveryContainer[data-status="error"] {
  border-color: var(--color-error);
  background: var(--color-error-light, #fef2f2);
}

.recoveryContainer[data-status="error"] .statusMessage {
  border-left-color: var(--color-error);
  background: var(--color-error-light, #fef2f2);
  color: var(--color-error-dark, #7f1d1d);
}

/* Warning state */
.recoveryContainer[data-status="warning"] {
  border-color: var(--color-warning);
  background: var(--color-warning-light, #fffbeb);
}

.recoveryContainer[data-status="warning"] .statusMessage {
  border-left-color: var(--color-warning);
  background: var(--color-warning-light, #fffbeb);
  color: var(--color-warning-dark, #92400e);
}
