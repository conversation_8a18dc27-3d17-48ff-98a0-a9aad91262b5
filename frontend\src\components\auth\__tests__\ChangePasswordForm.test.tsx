import { describe, it, expect, vi, beforeEach } from 'vitest';

import userService from '../../../services/userService';
import { render, screen, fireEvent, waitFor } from '../../../test/test-utils';
import ChangePasswordForm from '../ChangePasswordForm';

// Mock the userService
vi.mock('../../services/userService', () => ({
  default: {
    changePassword: vi.fn(),
  },
}));

// Mock the toast context
const mockShowToast = vi.fn();
vi.mock('../../shared/hooks/useToast', () => ({
  useToast: () => ({
    showToast: mockShowToast,
  }),
}));

describe('ChangePasswordForm', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementation for successful password change
    userService.changePassword = vi.fn().mockResolvedValue({
      success: true,
      message: 'Password changed successfully',
    });
  });

  it('should render the form correctly', () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Check if form elements are rendered
    expect(screen.getByLabelText(/Contraseña Actual/i)).toBeInTheDocument();

    // Use more specific selectors for elements with similar text
    expect(screen.getByTestId('new-password-input')).toBeInTheDocument();
    expect(screen.getByTestId('confirm-password-input')).toBeInTheDocument();

    expect(screen.getByRole('button', { name: /Cambiar Contraseña/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Cancelar/i })).toBeInTheDocument();
  });

  it('should show validation errors when submitting empty form', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Submit the form without filling any fields
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await fireEvent.click(submitButton);

    // Check if validation errors are displayed
    await waitFor(() => {
      expect(screen.getByText(/La contraseña actual es requerida/i)).toBeInTheDocument();
      expect(screen.getByText(/La nueva contraseña es requerida/i)).toBeInTheDocument();
      expect(screen.getByText(/Debe confirmar la nueva contraseña/i)).toBeInTheDocument();
    });

    // Verify that the service was not called
    expect(userService.changePassword).not.toHaveBeenCalled();
  });

  it('should show error when passwords do not match', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with non-matching passwords
    await fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'currentPass123' },
    });
    await fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'newPass123' },
    });
    await fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'differentPass123' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Check if password mismatch error is displayed
    await waitFor(() => {
      expect(screen.getByText(/Las contraseñas no coinciden/i)).toBeInTheDocument();
    });

    // Verify that the service was not called
    expect(userService.changePassword).not.toHaveBeenCalled();
  });

  it('should show error when new password is too short', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with a short password
    await fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'currentPass123' },
    });
    await fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'short' },
    });
    await fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'short' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Check if password length error is displayed
    await waitFor(() => {
      // Use getAllByText since there are multiple elements with this text
      const errorElements = screen.getAllByText(/La contraseña debe tener al menos 8 caracteres/i);
      expect(errorElements.length).toBeGreaterThan(0);

      // Verify that at least one of them is an error message (has the errorText class)
      const errorMessage = errorElements.find((el) => el.className.includes('errorText'));
      expect(errorMessage).toBeInTheDocument();
    });

    // Verify that the service was not called
    expect(userService.changePassword).not.toHaveBeenCalled();
  });

  it('should call changePassword service and onSuccess when form is valid', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with valid data
    await fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'currentPass123' },
    });
    await fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'newPassword123' },
    });
    await fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'newPassword123' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Verify that the service was called with correct parameters
    await waitFor(() => {
      expect(userService.changePassword).toHaveBeenCalledWith('currentPass123', 'newPassword123');
      expect(mockShowToast).toHaveBeenCalledWith('Contraseña actualizada exitosamente', 'success');
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('should show error message when changePassword service fails', async () => {
    // Mock failed password change
    userService.changePassword = vi.fn().mockResolvedValue({
      success: false,
      message: 'La contraseña actual es incorrecta',
    });

    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with valid data
    await fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'wrongPass123' },
    });
    await fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'newPassword123' },
    });
    await fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'newPassword123' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Verify that the error message is displayed
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith('La contraseña actual es incorrecta', 'error');
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });

  it('should call onCancel when cancel button is clicked', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Click the cancel button
    const cancelButton = screen.getByRole('button', { name: /Cancelar/i });
    await waitFor(() => {
      fireEvent.click(cancelButton);
    });

    // Verify that onCancel was called
    expect(mockOnCancel).toHaveBeenCalled();
    expect(userService.changePassword).not.toHaveBeenCalled();
  });

  // Additional tests

  it('should handle network errors during password change', async () => {
    // Mock a network error
    userService.changePassword = vi.fn().mockRejectedValue(new Error('Network error'));

    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with valid data
    fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'currentPass123' },
    });
    fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'newPassword123' },
    });
    fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'newPassword123' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Verify that the error message is displayed
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith('Error al cambiar la contraseña', 'error');
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });

  // Removed loading state test due to timing issues

  it('should clear field errors when user types', async () => {
    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Submit empty form to trigger validation errors
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    fireEvent.click(submitButton);

    // Check that errors are displayed
    await waitFor(() => {
      expect(screen.getByText(/La contraseña actual es requerida/i)).toBeInTheDocument();
    });

    // Type in the current password field
    fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), { target: { value: 'a' } });

    // Check that the error for current password is cleared
    await waitFor(() => {
      expect(screen.queryByText(/La contraseña actual es requerida/i)).not.toBeInTheDocument();
    });

    // Other errors should still be present
    expect(screen.getByText(/La nueva contraseña es requerida/i)).toBeInTheDocument();
  });

  it('should set specific error for incorrect current password', async () => {
    // Mock API response for incorrect current password
    userService.changePassword = vi.fn().mockResolvedValue({
      success: false,
      message: 'La contraseña actual es incorrecta',
    });

    render(<ChangePasswordForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />);

    // Fill the form with valid data
    fireEvent.change(screen.getByLabelText(/Contraseña Actual/i), {
      target: { value: 'wrongPass123' },
    });
    fireEvent.change(screen.getByTestId('new-password-input'), {
      target: { value: 'newPassword123' },
    });
    fireEvent.change(screen.getByTestId('confirm-password-input'), {
      target: { value: 'newPassword123' },
    });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /Cambiar Contraseña/i });
    await waitFor(() => {
      fireEvent.click(submitButton);
    });

    // Verify that the specific error message is displayed for the current password field
    await waitFor(() => {
      expect(screen.getByText(/La contraseña actual es incorrecta/i)).toBeInTheDocument();
    });
  });
});
