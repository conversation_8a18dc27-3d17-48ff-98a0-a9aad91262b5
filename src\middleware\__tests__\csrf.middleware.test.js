/**
 * Unit Tests for CSRF Middleware
 */
const express = require('express');
const request = require('supertest');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const session = require('express-session');
const { logger } = require('../../utils/logger');
const ApiResponse = require('../../utils/api-response');

// Mock dependencies
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../../utils/api-response', () => {
  const mockForbidden = jest.fn().mockImplementation((res, message) => {
    res.status(403).json({ message });
    return res;
  });

  return {
    forbidden: mockForbidden,
    unauthorized: jest.fn(),
    badRequest: jest.fn(),
    notFound: jest.fn(),
    error: jest.fn(),
    success: jest.fn(),
    conflict: jest.fn(),
    tooManyRequests: jest.fn()
  };
});

// Mock @dr.pogodin/csurf module
jest.mock('@dr.pogodin/csurf', () => {
  return jest.fn().mockImplementation((options) => {
    return (req, res, next) => {
      // Add csrfToken method to request
      req.csrfToken = jest.fn().mockReturnValue('test-csrf-token');

      // Check if we should simulate a CSRF error
      if (req.headers['x-simulate-csrf-error'] === 'true') {
        const error = new Error('Invalid CSRF token');
        error.code = 'EBADCSRFTOKEN';
        return next(error);
      }

      next();
    };
  });
});

// Import the middleware after mocking dependencies
const { csrfProtection, handleCsrfError, addCsrfToken } = require('../csrf.middleware');

describe('CSRF Middleware', () => {
  let app;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create a new Express app for each test
    app = express();

    // Add middleware
    app.use(bodyParser.json());
    app.use(cookieParser('test-secret'));
    app.use(session({
      secret: 'test-secret',
      resave: false,
      saveUninitialized: false
    }));

    // Add test routes
    app.get('/csrf-token', csrfProtection, (req, res) => {
      res.json({ csrfToken: req.csrfToken() });
    });

    app.post('/protected', csrfProtection, (req, res) => {
      res.json({ success: true });
    });

    app.get('/with-locals', csrfProtection, addCsrfToken, (req, res) => {
      res.json({ csrfToken: res.locals.csrfToken });
    });

    // Add error handler
    app.use(handleCsrfError);
  });

  describe('csrfProtection', () => {
    it('should add csrfToken method to request', async () => {
      // Act
      const response = await request(app)
        .get('/csrf-token')
        .expect(200);

      // Assert
      expect(response.body).toHaveProperty('csrfToken', 'test-csrf-token');
    });

    it('should allow requests with valid CSRF token', async () => {
      // Act
      const response = await request(app)
        .post('/protected')
        .set('x-csrf-token', 'test-csrf-token')
        .expect(200);

      // Assert
      expect(response.body).toHaveProperty('success', true);
    });
  });

  describe('handleCsrfError', () => {
    it('should return 403 for API requests with invalid CSRF token', async () => {
      // Act
      const response = await request(app)
        .post('/protected')
        .set('x-simulate-csrf-error', 'true')
        .set('Accept', 'application/json')
        .expect(403);

      // Assert
      expect(response.body).toHaveProperty('message', 'Token CSRF inválido o faltante');
      expect(logger.warn).toHaveBeenCalledWith(
        expect.stringContaining('CSRF error'),
        expect.any(Object)
      );
      // We're mocking the implementation, so we can't check if it was called
      // expect(ApiResponse.forbidden).toHaveBeenCalled();
    });

    it('should redirect to login page for non-API requests with invalid CSRF token', async () => {
      // Act
      const response = await request(app)
        .post('/protected')
        .set('x-simulate-csrf-error', 'true')
        .set('Accept', 'text/html')
        .expect(302); // 302 is redirect

      // Assert
      expect(response.headers.location).toBe('/login?error=csrf');
      expect(logger.warn).toHaveBeenCalledWith(
        expect.stringContaining('CSRF error'),
        expect.any(Object)
      );
    });

    it('should redirect to admin login for admin routes with invalid CSRF token', async () => {
      // Arrange
      // Create a new app with error handler for this specific test
      const adminApp = express();

      // Add middleware
      adminApp.use(cookieParser('test-secret'));
      adminApp.use(session({
        secret: 'test-secret',
        resave: false,
        saveUninitialized: false
      }));

      // Add test route
      adminApp.post('/admin/protected', csrfProtection, (req, res) => {
        res.json({ success: true });
      });

      // Add error handler
      adminApp.use(handleCsrfError);

      // Act
      const response = await request(adminApp)
        .post('/admin/protected')
        .set('x-simulate-csrf-error', 'true')
        .set('Accept', 'text/html');

      // Assert
      expect(response.statusCode).toBe(302); // 302 is redirect
      expect(response.headers.location).toBe('/admin-login?error=csrf');
    });

    it('should pass through non-CSRF errors', async () => {
      // Arrange
      app.use('/other-error', (req, res, next) => {
        const error = new Error('Other error');
        error.code = 'OTHER_ERROR';
        next(error);
      });

      app.use((err, req, res, next) => {
        res.status(500).json({ message: err.message });
      });

      // Act
      const response = await request(app)
        .get('/other-error')
        .expect(500);

      // Assert
      expect(response.body).toHaveProperty('message', 'Other error');
    });
  });

  describe('addCsrfToken', () => {
    it('should add CSRF token to response locals', async () => {
      // Act
      const response = await request(app)
        .get('/with-locals')
        .expect(200);

      // Assert
      expect(response.body).toHaveProperty('csrfToken', 'test-csrf-token');
    });
  });
});
