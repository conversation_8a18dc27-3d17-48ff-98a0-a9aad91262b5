// Memory cleanup utilities to prevent memory leaks
import { logger } from './logger';

// Track all active intervals and timeouts
const activeTimers = new Set<number>();
const activeIntervals = new Set<number>();

// Store original functions
let originalSetTimeout: typeof window.setTimeout;
let originalClearTimeout: typeof window.clearTimeout;
let originalSetInterval: typeof window.setInterval;
let originalClearInterval: typeof window.clearInterval;

// Flag to control timer tracking
let isTimerTrackingEnabled = false;

// Initialize timer tracking (call this function after app initialization)
// NOTE: This adds overhead to all timer operations and should only be enabled for debugging
export const initializeTimerTracking = (enable = false) => {
  if (!enable) {
    logger.debug('Timer tracking disabled - no overhead added to timer operations');
    return;
  }
  
  isTimerTrackingEnabled = true;
  // Store original functions
  originalSetTimeout = window.setTimeout;
  originalClearTimeout = window.clearTimeout;
  originalSetInterval = window.setInterval;
  originalClearInterval = window.clearInterval;

  // Override setTimeout to track timers
  (window as any).setTimeout = function(handler: TimerHandler, timeout?: number, ...args: any[]): number {
    const timer = originalSetTimeout(handler as any, timeout, ...args) as unknown as number;
    activeTimers.add(timer);
    return timer;
  };

  // Override clearTimeout to remove from tracking
  (window as any).clearTimeout = function(timer: number | undefined): void {
    if (timer !== undefined) {
      activeTimers.delete(timer);
      originalClearTimeout.call(window, timer as any);
    }
  };

  // Override setInterval to track intervals
  (window as any).setInterval = function(handler: TimerHandler, timeout?: number, ...args: any[]): number {
    const interval = originalSetInterval(handler as any, timeout, ...args) as unknown as number;
    activeIntervals.add(interval);
    return interval;
  };

  // Override clearInterval to remove from tracking
  (window as any).clearInterval = function(interval: number | undefined): void {
    if (interval !== undefined) {
      activeIntervals.delete(interval);
      originalClearInterval.call(window, interval as any);
    }
  };
};

// Clean up all active timers and intervals
export const cleanupAllTimers = () => {
  if (!isTimerTrackingEnabled) {
    return;
  }
  
  logger.info(`Cleaning up ${activeTimers.size} timers and ${activeIntervals.size} intervals`);
  
  // Clear all timeouts
  activeTimers.forEach(timer => {
    if (originalClearTimeout) {
      originalClearTimeout.call(window, timer as any);
    } else {
      clearTimeout(timer);
    }
  });
  
  // Clear all intervals
  activeIntervals.forEach(interval => {
    if (originalClearInterval) {
      originalClearInterval.call(window, interval as any);
    } else {
      clearInterval(interval);
    }
  });
  
  activeTimers.clear();
  activeIntervals.clear();
};

// Monitor memory usage
export const logMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    logger.info('Memory usage:', {
      usedJSHeapSize: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
      totalJSHeapSize: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
      jsHeapSizeLimit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
    });
  }
};

// Cleanup event listeners on route change
export const cleanupEventListeners = () => {
  // Remove any body styles that might have been set
  document.body.style.overflow = '';
  
  // Note: Components should clean up their own event listeners.
  // Cloning the entire DOM is extremely expensive and causes performance issues.
  // If specific global listeners need cleanup, they should be tracked and removed individually.
  logger.debug('Event listener cleanup called - components should handle their own cleanup');
};