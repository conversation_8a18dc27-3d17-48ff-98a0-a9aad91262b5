const db = require('./src/db');
const { ApplicationStatus } = require('./src/constants/application.constants');

async function triggerPdfRegeneration() {
  try {
    console.log('Triggering PDF regeneration for permits with missing files...');
    
    // Update permits 14 and 36 to PAYMENT_RECEIVED status to trigger PDF generation
    const permitIds = [14, 36];
    
    for (const permitId of permitIds) {
      console.log(`\nUpdating permit ${permitId}...`);
      
      // First, check current status
      const checkQuery = `
        SELECT id, status, permit_file_path, user_id 
        FROM permit_applications 
        WHERE id = $1
      `;
      
      const checkResult = await db.query(checkQuery, [permitId]);
      
      if (checkResult.rows.length === 0) {
        console.log(`❌ Permit ${permitId} not found`);
        continue;
      }
      
      const permit = checkResult.rows[0];
      console.log(`Current status: ${permit.status}`);
      console.log(`Current file path: ${permit.permit_file_path || 'NULL'}`);
      
      // Update status to PAYMENT_RECEIVED to trigger PDF generation
      const updateQuery = `
        UPDATE permit_applications 
        SET 
          status = $1,
          updated_at = NOW(),
          queue_status = NULL,
          queue_job_id = NULL,
          queue_entered_at = NULL,
          queue_started_at = NULL,
          queue_completed_at = NULL
        WHERE id = $2
        RETURNING id, status
      `;
      
      const updateResult = await db.query(updateQuery, [ApplicationStatus.PAYMENT_RECEIVED, permitId]);
      
      if (updateResult.rows.length > 0) {
        console.log(`✅ Permit ${permitId} updated to ${ApplicationStatus.PAYMENT_RECEIVED}`);
        console.log(`   This will trigger PDF generation automatically`);
      } else {
        console.log(`❌ Failed to update permit ${permitId}`);
      }
    }
    
    console.log('\n🎯 PDF regeneration triggered!');
    console.log('The PDF generation processor will pick up these permits and generate the missing files.');
    console.log('Check the PM2 logs to monitor progress: pm2 logs permisos-backend');
    
  } catch (error) {
    console.error('Error triggering PDF regeneration:', error.message);
  } finally {
    process.exit(0);
  }
}

triggerPdfRegeneration();
