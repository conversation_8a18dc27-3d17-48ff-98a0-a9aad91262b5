/*
 * Mobile Utilities
 * A collection of essential utility classes for implementing the mobile design system
 * Optimized for devices common in the Mexican market (360px width)
 */

/*
 * Text utilities
 * Apply these classes to make text responsive with fluid typography
 */
.u-text-fluid {
  font-size: var(--font-size-fluid-base);
}

.u-heading-fluid {
  font-size: var(--font-size-fluid-xl);
  line-height: 1.2;
}

.u-subheading-fluid {
  font-size: var(--font-size-fluid-lg);
  line-height: 1.3;
}

/*
 * Layout utilities
 * Apply these classes to create responsive layouts
 */
.u-stack-on-mobile {
  display: flex;
  flex-direction: row;
}

@media (max-width: var(--breakpoint-md)) {
  .u-stack-on-mobile {
    flex-direction: column;
  }
}

/*
 * Safe Area utilities
 * Apply these classes to handle device safe areas (notches, navigation bars)
 */
.u-safe-area-top {
  padding-top: var(--safe-area-inset-top);
}

.u-safe-area-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.u-safe-area-left {
  padding-left: var(--safe-area-inset-left);
}

.u-safe-area-right {
  padding-right: var(--safe-area-inset-right);
}

.u-safe-area-all {
  padding-top: var(--safe-area-inset-top);
  padding-right: var(--safe-area-inset-right);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
}
