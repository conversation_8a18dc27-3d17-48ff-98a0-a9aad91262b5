/* Verification Layout Styles */
.verificationPage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-neutral-100);
  overflow-x: hidden;
}

.desktopHeader {
  width: 100%;
  padding: var(--space-3) var(--space-5);
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-neutral-200);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--mobile-header-height, 56px); /* Match AppHeaderMobile */
  z-index: var(--z-fixed);
  box-sizing: border-box;
}

.verificationContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
  max-width: 550px;
  margin: 0 auto;
  padding: var(--space-4);
  padding-top: calc(var(--mobile-header-height, 56px) + var(--space-4));
  box-sizing: border-box;
}

/* REMOVE all .mobileMenu, .mobileMenuOpen, .mobileMenuOverlay, .mobileMenuContent, .mobileMenuLink styles */

@media (width <= 767px) {
  /* isMdDown breakpoint */
  .desktopHeader {
    display: none; /* This is handled by JSX, but good for clarity */
  }

  /* .verificationContainer padding-top is already correct for mobile due to above calc */
}

@media (max-width: var(--breakpoint-xs)) {
  /* 360px */
  .verificationContainer {
    padding-top: calc(var(--mobile-header-height-sm, 52px) + var(--space-3));
  }
}
