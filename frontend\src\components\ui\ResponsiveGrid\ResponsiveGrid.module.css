/* ResponsiveGrid styles */

/* Base grid styles */
.grid {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(var(--grid-columns-xl), 1fr);
  gap: var(--grid-gap-xl);
}

/* Responsive adjustments */
@media (width <= 1024px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-lg), 1fr);
    gap: var(--grid-gap-lg);
  }
}

@media (width <= 768px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-md), 1fr);
    gap: var(--grid-gap-md);
  }
}

@media (width <= 480px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-sm), 1fr);
    gap: var(--grid-gap-sm);
  }
}

@media (width <= 360px) {
  .grid {
    grid-template-columns: repeat(var(--grid-columns-xs), 1fr);
    gap: var(--grid-gap-xs);
  }
}
