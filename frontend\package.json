{"name": "permisos-digitales-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint src/", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\""}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^4.10.0", "@tanstack/react-query": "^5.81.2", "axios": "^1.7.9", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.1", "uuid": "^11.0.3", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/body-parser": "^1.19.6", "@types/istanbul-lib-report": "^3.0.3", "@types/qs": "^6.14.0", "@types/range-parser": "^1.2.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/send": "^0.17.5", "@types/serve-static": "^1.15.8", "@types/uuid": "^10.0.0", "@types/yargs-parser": "^21.0.3", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.17", "jsdom": "^26.0.0", "playwright": "^1.50.0", "prettier": "^3.4.2", "typescript": "^5.7.3", "vite": "^6.0.7", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}