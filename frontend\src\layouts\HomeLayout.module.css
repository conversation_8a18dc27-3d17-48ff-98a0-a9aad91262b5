/* Home Layout Styles */
.layoutContainer {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(
    --color-neutral-100
  ); /* Ensure the layout container has the correct background */
}

.mainContent {
  flex: 1;
  width: 100%;
  padding: 0;
  margin: 0;
}

/* Add a global style for the home page body */
:global(.home-body) {
  padding-top: 0 !important; /* Override the default padding-top for the header */
  background-color: var(--color-neutral-100) !important; /* Force the correct background color */
}
