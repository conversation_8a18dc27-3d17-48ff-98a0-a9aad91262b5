/* ResponsiveImage Component Styles */
.responsiveImage {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Object-fit variants */
.objectFitContain {
  object-fit: contain;
}

.objectFitCover {
  object-fit: cover;
}

.objectFitFill {
  object-fit: fill;
}

.objectFitNone {
  object-fit: none;
}

.objectFitScaleDown {
  object-fit: scale-down;
}

/* Optional aspect ratio container */
.aspectRatioContainer {
  position: relative;
  width: 100%;
  height: 0;
  overflow: hidden;
}

.aspectRatioContainer img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Common aspect ratios */
.ratio1x1 {
  padding-bottom: 100%; /* 1:1 */
}

.ratio4x3 {
  padding-bottom: 75%; /* 4:3 */
}

.ratio16x9 {
  padding-bottom: 56.25%; /* 16:9 */
}

.ratio3x2 {
  padding-bottom: 66.67%; /* 3:2 */
}

/* Fade-in animation for images */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-in;
}
