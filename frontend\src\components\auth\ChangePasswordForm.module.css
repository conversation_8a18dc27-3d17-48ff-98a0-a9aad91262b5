.formContainer {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.formTitle {
  font-size: 1.5rem;
  color: var(--color-primary-dark);
  margin-bottom: var(--space-3);
  text-align: center;
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.formGroup {
  margin-bottom: var(--space-3);
}

.formLabel {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin-bottom: var(--space-1);
}

.formInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formInput:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgb(var(--color-primary-rgb), 0.2);
}

.formInput:disabled {
  background-color: var(--color-neutral-100);
  cursor: not-allowed;
}

.inputError {
  border-color: var(--color-error);
}

.errorText {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: var(--space-1);
  margin-bottom: 0;
}

.helperText {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  margin-top: var(--space-1);
  margin-bottom: 0;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.actionButton {
  display: inline-block;
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgb(167 43 49 / 25%);
  position: relative;
  overflow: hidden;
}

.actionButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgb(255 255 255 / 10%), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.actionButton:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 5px 12px rgb(167 43 49 / 30%);
}

.actionButton:hover::before {
  transform: translateX(100%);
}

.actionButton:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgb(167 43 49 / 25%);
}

.actionButton:disabled {
  background-color: var(--color-neutral-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondaryButton {
  background-color: var(--color-neutral-200);
  color: var(--color-neutral-800);
}

.secondaryButton:hover {
  background-color: var(--color-neutral-300);
}

.secondaryButton:disabled {
  background-color: var(--color-neutral-200);
  color: var(--color-neutral-500);
  cursor: not-allowed;
}

/* Responsive Styles */
@media (width <= 768px) {
  .formContainer {
    padding: var(--space-2);
  }

  .formTitle {
    font-size: 1.3rem;
    margin-bottom: var(--space-2);
  }

  .form {
    gap: var(--space-2);
  }

  .formGroup {
    margin-bottom: var(--space-2);
  }

  .formLabel {
    display: block;
    width: 100%;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  .formInput {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    min-height: 44px; /* Better touch target */
  }

  .errorText {
    font-size: 0.85rem;
  }

  .helperText {
    font-size: 0.85rem;
  }

  /* Form Actions */
  .formActions {
    flex-direction: column-reverse; /* Primary button on top */
    gap: 0.75rem;
    margin-top: var(--space-3);
  }

  .actionButton {
    width: 100%;
    text-align: center;
    padding: 0.875rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 48px; /* Better touch target */
  }
}
