// Service Worker for Permisos Digitales
// Provides offline capabilities and caching for better performance

const CACHE_NAME = 'permisos-digitales-v1';

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/admin.html',
  '/img/logo.svg',
  '/img/logo-white.svg',
  '/img/favicon.svg',
  '/js/style-injector.js',
  '/offline.html'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker...');

  // Skip waiting to ensure the new service worker activates immediately
  self.skipWaiting();

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .catch((error) => {
        console.error('[Service Worker] Cache installation failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');

  // Claim clients to ensure the service worker controls all pages immediately
  event.waitUntil(self.clients.claim());

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((cacheName) => {
              // Delete any old caches that don't match the current version
              return cacheName !== CACHE_NAME;
            })
            .map((cacheName) => {
              console.log('[Service Worker] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            })
        );
      })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', (event) => {
  // Skip for API requests and browser-sync requests (during development)
  if (
    event.request.url.includes('/auth/') ||
    event.request.url.includes('/applications/') ||
    event.request.url.includes('/admin/') ||
    event.request.url.includes('/user/') ||
    event.request.url.includes('/payments/') ||
    event.request.url.includes('/webhook/') ||
    event.request.url.includes('browser-sync') ||
    event.request.url.includes('sockjs-node') ||
    event.request.url.includes('api.permisosdigitales.com.mx') ||
    event.request.method !== 'GET'
  ) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        // Return cached response if found
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise, fetch from network
        return fetch(event.request)
          .then((response) => {
            // Don't cache if not a valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response since it can only be consumed once
            const responseToCache = response.clone();

            // Cache the fetched response for future use
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });

            return response;
          })
          .catch((error) => {
            console.log('[Service Worker] Fetch failed; returning offline page instead.', error);

            // If it's a navigation request (for a page), show the offline page
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html');
            }

            // For image requests, return a placeholder
            if (event.request.destination === 'image') {
              return caches.match('/img/offline-image-placeholder.svg');
            }

            // For other resources, return a simple response
            return new Response('Network error happened', {
              status: 408,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

// Handle messages from the client
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
