/* Loading Spinner Styles */
.spinnerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 2rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgb(0 0 0 / 10%);
  border-radius: 50%;
  border-top-color: var(--color-primary, #a72b31);
  animation: spin 1s ease-in-out infinite;
}

/* Inline Spinner for buttons */
.inlineSpinner {
  display: inline-block;
  border: 2px solid rgb(255 255 255 / 30%);
  border-radius: 50%;
  border-top-color: currentColor;
  border-right-color: currentColor;
  animation: spin 0.6s linear infinite;
  vertical-align: middle;
}

/* Inline spinner sizes */
.size-sm {
  width: 0.75rem;
  height: 0.75rem;
}

.size-md {
  width: 1rem;
  height: 1rem;
}

.size-lg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Spin animation keyframes */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
