/* Applications Page Styles */
/* Container styling now handled by ResponsiveContainer component */

/* Page Header */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem 0;
}

.pageSubtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Mobile Table Styles */
.mobileTable {
  margin-bottom: 1.5rem;
}

/* Page Header */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem;
}

.pageSubtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
}

/* Search Container */
.searchContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.searchInputWrapper {
  position: relative;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.875rem;
}

.searchInput {
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid #ced4da;
  font-size: 0.875rem;
  min-width: 250px;
  border-radius: 4px;
  height: 44px; /* Ensure touch-friendly height */
}

.searchInput:focus {
  outline: none;
  border-color: #a72b31;
}

.filterContainer {
  display: flex;
  gap: 0.5rem;
}

.filterWrapper {
  position: relative;
}

.filterIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.875rem;
}

.filterSelect {
  padding: 0.5rem 0.75rem 0.5rem 2.25rem;
  border: 1px solid #ced4da;
  font-size: 0.875rem;
  min-width: 200px;
  height: 44px; /* Ensure touch-friendly height */
  border-radius: 4px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M.5 1.5l3 3 3-3H.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 8px 8px;
}

.filterSelect:focus {
  outline: none;
  border-color: #a72b31;
}

.refreshButton {
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  color: #343a40;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  min-height: 44px; /* Ensure touch-friendly height */
  min-width: 44px;
}

.refreshButton:hover {
  background-color: #e9ecef;
}

/* Mobile Cards */
.mobileCards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileCard {
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
}

.mobileCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
  border-color: #a72b31;
}

.mobileCard:active {
  transform: translateY(0);
}

.mobileCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.mobileCardTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #343a40;
  line-height: 1.3;
}

.mobileCardId {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
}

.mobileCardContent {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobileCardItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
}

.mobileCardLabel {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 1rem;
}

.mobileCardValue {
  font-size: 0.875rem;
  color: #343a40;
  text-align: right;
  word-break: break-word;
}

.mobileCardActions {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: center;
}

/* Responsive styles for tablets */
@media (width <= 768px) {
  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .pageTitle {
    font-size: 1.5rem;
  }

  .searchContainer {
    width: 100%;
    flex-direction: column;
    gap: 1rem;
  }

  .searchInput {
    width: 100%;
  }

  .filterContainer {
    width: 100%;
    flex-direction: column;
    gap: 1rem;
  }

  .filterWrapper {
    width: 100%;
    margin-right: 0;
  }

  .filterSelect {
    width: 100%;
}

/* Mobile styles */
@media (width <= 480px) {
  .pageHeader {
    margin-bottom: 1rem;
  }

  .pageTitle {
    font-size: 1.25rem;
  }

  .pageSubtitle {
    font-size: 0.875rem;
  }

  .searchInput {
    font-size: 0.8rem;
  }

  .filterSelect {
    font-size: 0.8rem;
  }

  /* Mobile card improvements */
  .mobileCard {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  .mobileCardTitle {
    font-size: 1rem;
  }

  .mobileCardLabel,
  .mobileCardValue {
    font-size: 0.8rem;
  }

  .mobileCardId {
    font-size: 0.8rem;
}

/* Table Styles */
.tableContainer {
  overflow-x: auto;
}

.applicationsTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.applicationsTable th,
.applicationsTable td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.applicationsTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #343a40;
}

.applicationsTable tr:hover {
  background-color: #f8f9fa;
}

/* Status Badge */
.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
  min-height: 28px;
  min-width: 100px;
  text-align: center;
  white-space: nowrap;
}

.statusPending {
  background-color: #fff3cd;
  color: #856404;
}

.statusSubmitted {
  background-color: #cce5ff;
  color: #004085;
}

.statusVerified {
  background-color: #d4edda;
  color: #155724;
}

.statusRejected {
  background-color: #f8d7da;
  color: #721c24;
}

.statusReady {
  background-color: #d1ecf1;
  color: #0c5460;
}

.statusCompleted {
  background-color: #d4edda;
  color: #155724;
}

.statusCancelled {
  background-color: #e2e3e5;
  color: #383d41;
}

.statusGenerating {
  background-color: #fff3cd;
  color: #856404;
}

.statusError {
  background-color: #f8d7da;
  color: #721c24;
}

.statusExpired {
  background-color: #e2e3e5;
  color: #383d41;
}

/* Responsive status badge styles */
@media (width <= 576px) {
  .statusBadge {
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    min-height: 32px;
    min-width: 110px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

@media (width <= 360px) {
  .statusBadge {
    width: 100%;
    margin: 0.25rem 0;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.viewButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #ced4da;
  background-color: white;
  color: #0d6efd;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
  min-width: 44px; /* Minimum touch target size */
  min-height: 44px; /* Minimum touch target size */
}

.viewButton:hover {
  background-color: #e6f7ff;
  border-color: #0d6efd;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

.viewButtonDisabled {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #ced4da;
  background-color: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
  opacity: 0.5;
  min-width: 44px; /* Minimum touch target size */
  min-height: 44px; /* Minimum touch target size */
}

/* Responsive action button styles */
@media (width <= 576px) {
  .actionButtons {
    justify-content: flex-end;
  }

  .viewButton,
  .viewButtonDisabled {
    width: 48px;
    height: 48px;
    font-size: 1.1rem;
}

@media (width <= 360px) {
  .actionButtons {
    justify-content: center;
    width: 100%;
    margin-top: 0.5rem;
}

/* Pagination styles removed as we're using the built-in pagination from MobileTable */

/* Empty State */
.emptyState {
  text-align: center;
  padding: 3rem;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin: 1rem 0;
}

.emptyState h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
  margin: 0 0 0.5rem;
}

.emptyState p {
  font-size: 1rem;
  color: #6c757d;
  margin: 0 0 1.5rem;
}

.createTestButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #a72b31;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease;
  min-height: 44px; /* Minimum touch target size */
  border-radius: 4px;
}

.createTestButton:hover:not(:disabled) {
  background-color: #852d2d;
}

.createTestButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Responsive empty state and loading/error containers */
@media (width <= 768px) {
  .emptyState,
  .loadingContainer,
  .errorContainer {
    padding: 2rem;
}

@media (width <= 480px) {
  .emptyState,
  .loadingContainer,
  .errorContainer {
    padding: 1.5rem;
  }

  .emptyState h2 {
    font-size: 1.1rem;
  }

  .emptyState p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgb(0 0 0 / 10%);
  border-radius: 50%;
  border-top-color: #a72b31;
  animation: spin var(--animation-duration-base) linear infinite;
  margin-bottom: 1rem;
  will-change: transform; /* Optimize animation performance */
}


.errorIcon {
  font-size: 3rem;
  color: #a72b31;
  margin-bottom: 1rem;
}

.retryButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #a72b31;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.15s ease;
  min-height: 44px; /* Minimum touch target size */
  min-width: 120px; /* Ensure adequate width */
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.retryButton:hover {
  background-color: #852d2d;
}

/* Responsive styles for error icon and retry button */
@media (width <= 480px) {
  .errorIcon {
    font-size: 2.5rem;
  }

  .retryButton {
    width: 100%;
    max-width: 200px;
  }
}
}
}
}
}
}
}
}
}
