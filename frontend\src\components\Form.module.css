.formContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.formTitle {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.formGroup {
  margin-bottom: 15px;
}

.label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgb(74 144 226 / 20%);
}

.error {
  color: #e53935;
  font-size: 14px;
  margin-top: 5px;
}

.submitButton {
  padding: 12px 24px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submitButton:hover {
  background-color: #3a7bc8;
}

.submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.backButton {
  padding: 12px 24px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  margin-right: 10px;
  transition: background-color 0.3s;
}

.backButton:hover {
  background-color: #e8e8e8;
}

.buttonContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.stepIndicator {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.step {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 5px;
  font-weight: bold;
}

.stepActive {
  background-color: #4a90e2;
  color: white;
}

.stepCompleted {
  background-color: #4caf50;
  color: white;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
