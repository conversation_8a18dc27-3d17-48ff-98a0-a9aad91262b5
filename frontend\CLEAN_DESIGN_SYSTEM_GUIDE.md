# Clean Design System Guide

## Overview
I've created a **completely isolated** design system that shows what your UI SHOULD look like. This is not using any of your existing components or styles - it's a fresh start showing best practices.

Visit: `http://localhost:3002/design-system`

## Key Design Decisions

### 1. Color Palette
- **Primary Red**: `#B5384D` (This is your brand color - use it EVERYWHERE)
- **Never use**: Different shades of red like #B71C1C, #DC2626, etc.
- **Status Colors**: Green for success, Yellow for warning, Red for error, Blue for info

### 2. Typography
- **Font**: System font stack (no custom fonts needed)
- **Sizes**: 2.5rem → 2rem → 1.5rem → 1.25rem → 1rem → 0.875rem
- **Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Line Height**: 1.6 for body text

### 3. Spacing
- **Base Unit**: 8px
- **Scale**: 8px, 16px, 24px, 32px, 48px, 64px
- **Never use**: Random values like 15px, 20px, etc.

### 4. Components

#### Buttons
```css
/* Primary Button */
background-color: #B5384D;
color: white;
padding: 0.5rem 1.5rem;
border-radius: 6px;
border: none;
font-weight: 500;

/* Secondary Button */
background-color: white;
color: #B5384D;
border: 2px solid #B5384D;
```

#### Form Inputs
```css
width: 100%;
padding: 0.625rem 0.875rem;
border: 1px solid #ced4da;
border-radius: 6px;
font-size: 1rem;

/* Focus state */
border-color: #B5384D;
box-shadow: 0 0 0 3px rgba(181, 56, 77, 0.1);
```

#### Cards
```css
background-color: white;
border-radius: 8px;
padding: 1.5rem;
box-shadow: 0 2px 4px rgba(0,0,0,0.04);
```

## How to Fix Your Current UI

### Step 1: Compare
Open your app and the design system side by side. For each page:
1. Look at buttons - are they using the right color?
2. Check spacing - is it consistent?
3. Review text sizes - do they match the scale?

### Step 2: Update Colors
Find and replace in your CSS:
- Any red that's not `#B5384D` → Replace with `#B5384D`
- Create CSS variables:
```css
:root {
  --color-primary: #B5384D;
  --color-primary-dark: #8A2B3A;
  --color-success: #198754;
  --color-error: #dc3545;
}
```

### Step 3: Fix Buttons
Replace all custom button styles with:
```jsx
// Good
<button className="btn btn-primary">Click me</button>

// Bad
<button style={{background: 'red'}}>Click me</button>
```

### Step 4: Standardize Forms
All inputs should look the same:
```jsx
<input 
  type="text" 
  className="form-control"
  placeholder="Enter text..."
/>
```

### Step 5: Fix Mobile Navigation
The navigation should:
- Have proper z-index (1000+)
- Use consistent colors
- Have proper spacing

## Quick Wins

1. **Global CSS Reset**: Add this to your main CSS:
```css
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: #212529;
  line-height: 1.6;
}
```

2. **Button Classes**: Create these classes:
```css
.btn {
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #B5384D;
  color: white;
  border: none;
}

.btn-secondary {
  background: white;
  color: #B5384D;
  border: 2px solid #B5384D;
}
```

3. **Form Consistency**: 
```css
.form-control {
  width: 100%;
  padding: 0.625rem 0.875rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
}

.form-control:focus {
  border-color: #B5384D;
  outline: none;
  box-shadow: 0 0 0 3px rgba(181, 56, 77, 0.1);
}
```

## Testing Your Fixes

1. **Color Test**: Take a screenshot and check if all reds are #B5384D
2. **Spacing Test**: Measure spacing between elements - should be 8, 16, 24, 32px
3. **Mobile Test**: Navigation should work without overlay issues
4. **Form Test**: All inputs should look identical

## Remember

- **Don't mix inline styles with classes**
- **Don't use random color values**
- **Don't use arbitrary spacing**
- **Do use the design system as reference**
- **Do test on mobile devices**

The clean design system at `/design-system` is your north star. Every UI element in your app should match what you see there.